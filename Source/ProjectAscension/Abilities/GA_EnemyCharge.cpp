#include "GA_EnemyCharge.h"
#include "../Characters/SurvivorsCharacterBase.h"
#include "../Characters/SurvivorsPlayerCharacter.h"
#include "../GAS/SurvivorsAbilitySystemComponent.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"

UGA_EnemyCharge::UGA_EnemyCharge()
{
    AbilityInputID = EAbilityInputID::None;
    InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerActor;
    
    // Set ability tags
    AbilityTags.AddTag(FSurvivorsGameplayTags::Get().Ability_Charge);
    
    // Set default values
    ChargeDistance = 800.0f;
    ChargeDuration = 1.0f;
    ChargeDamage = 50.0f;
    ChargeRadius = 150.0f;
    ChargeCooldown = 5.0f;
    ChargeSpeed = 1200.0f;
}

void UGA_EnemyCharge::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
    if (!CommitAbility(Handle, ActorInfo, ActivationInfo))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Get the character
    ASurvivorsCharacterBase* Character = GetSurvivorsCharacterFromActorInfo();
    if (!Character)
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Find target (player)
    ASurvivorsPlayerCharacter* Player = FindPlayerTarget();
    if (!Player)
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Calculate charge direction
    FVector ChargeDirection = (Player->GetActorLocation() - Character->GetActorLocation()).GetSafeNormal();
    FVector ChargeTarget = Character->GetActorLocation() + (ChargeDirection * ChargeDistance);

    // Store charge info
    ChargeStartLocation = Character->GetActorLocation();
    ChargeTargetLocation = ChargeTarget;
    ChargeDirection = ChargeDirection;
    ChargeElapsedTime = 0.0f;
    bHasHitTarget = false;

    // Start charge
    StartCharge(Character);

    // Set timer for charge completion
    FTimerHandle ChargeTimer;
    GetWorld()->GetTimerManager().SetTimer(ChargeTimer, [this, Handle, ActorInfo, ActivationInfo]()
    {
        CompleteCharge(Handle, ActorInfo, ActivationInfo);
    }, ChargeDuration, false);
}

void UGA_EnemyCharge::StartCharge(ASurvivorsCharacterBase* Character)
{
    if (!Character)
    {
        return;
    }

    // Increase movement speed for charge
    if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
    {
        OriginalMaxWalkSpeed = MovementComp->MaxWalkSpeed;
        MovementComp->MaxWalkSpeed = ChargeSpeed;
    }

    // Add charge tag
    if (USurvivorsAbilitySystemComponent* ASC = GetSurvivorsAbilitySystemComponentFromActorInfo())
    {
        ASC->AddLooseGameplayTag(FSurvivorsGameplayTags::Get().Ability_Charge);
        ASC->AddLooseGameplayTag(FSurvivorsGameplayTags::Get().Character_State_Moving);
    }

    // Start charge movement
    GetWorld()->GetTimerManager().SetTimer(ChargeUpdateTimer, [this, Character]()
    {
        UpdateChargeMovement(Character);
    }, 0.016f, true); // ~60 FPS updates
}

void UGA_EnemyCharge::UpdateChargeMovement(ASurvivorsCharacterBase* Character)
{
    if (!Character)
    {
        return;
    }

    ChargeElapsedTime += 0.016f;
    
    // Move character in charge direction
    FVector CurrentLocation = Character->GetActorLocation();
    FVector NewLocation = CurrentLocation + (ChargeDirection * ChargeSpeed * 0.016f);
    Character->SetActorLocation(NewLocation);

    // Check for collision with player during charge
    CheckChargeCollision(Character);

    // Check if we've reached the target distance
    float DistanceTraveled = FVector::Dist(ChargeStartLocation, Character->GetActorLocation());
    if (DistanceTraveled >= ChargeDistance)
    {
        // Stop charge early if we've traveled far enough
        GetWorld()->GetTimerManager().ClearTimer(ChargeUpdateTimer);
    }
}

void UGA_EnemyCharge::CheckChargeCollision(ASurvivorsCharacterBase* Character)
{
    if (!Character || bHasHitTarget)
    {
        return;
    }

    // Find nearby players
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ASurvivorsPlayerCharacter::StaticClass(), FoundActors);

    for (AActor* Actor : FoundActors)
    {
        if (ASurvivorsPlayerCharacter* Player = Cast<ASurvivorsPlayerCharacter>(Actor))
        {
            float Distance = FVector::Dist(Character->GetActorLocation(), Player->GetActorLocation());
            if (Distance <= ChargeRadius)
            {
                // Apply damage to player
                ApplyChargeDamage(Player);
                bHasHitTarget = true;
                
                // Knockback the player
                ApplyKnockback(Player, Character);
                break;
            }
        }
    }
}

void UGA_EnemyCharge::ApplyChargeDamage(ASurvivorsPlayerCharacter* Player)
{
    if (!Player || !ChargeDamageEffect)
    {
        return;
    }

    if (USurvivorsAbilitySystemComponent* PlayerASC = Cast<USurvivorsAbilitySystemComponent>(Player->GetAbilitySystemComponent()))
    {
        ApplyEffectToTarget(ChargeDamageEffect, PlayerASC, GetAbilityLevel());
        
        UE_LOG(LogTemp, Log, TEXT("Enemy charge hit player for %f damage"), ChargeDamage);
    }
}

void UGA_EnemyCharge::ApplyKnockback(ASurvivorsPlayerCharacter* Player, ASurvivorsCharacterBase* Charger)
{
    if (!Player || !Charger)
    {
        return;
    }

    // Calculate knockback direction
    FVector KnockbackDirection = (Player->GetActorLocation() - Charger->GetActorLocation()).GetSafeNormal();
    
    // Apply knockback force
    if (UCharacterMovementComponent* PlayerMovement = Player->GetCharacterMovement())
    {
        FVector KnockbackForce = KnockbackDirection * KnockbackStrength;
        PlayerMovement->AddImpulse(KnockbackForce, true);
    }
}

void UGA_EnemyCharge::CompleteCharge(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo)
{
    ASurvivorsCharacterBase* Character = GetSurvivorsCharacterFromActorInfo();
    if (Character)
    {
        // Restore normal movement speed
        if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
        {
            MovementComp->MaxWalkSpeed = OriginalMaxWalkSpeed;
        }

        // Remove charge tags
        if (USurvivorsAbilitySystemComponent* ASC = GetSurvivorsAbilitySystemComponentFromActorInfo())
        {
            ASC->RemoveLooseGameplayTag(FSurvivorsGameplayTags::Get().Ability_Charge);
            ASC->RemoveLooseGameplayTag(FSurvivorsGameplayTags::Get().Character_State_Moving);
        }
    }

    // Clear charge timer
    GetWorld()->GetTimerManager().ClearTimer(ChargeUpdateTimer);

    // End ability
    EndAbility(Handle, ActorInfo, ActivationInfo, true, false);
}

ASurvivorsPlayerCharacter* UGA_EnemyCharge::FindPlayerTarget() const
{
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ASurvivorsPlayerCharacter::StaticClass(), FoundActors);

    if (FoundActors.Num() > 0)
    {
        return Cast<ASurvivorsPlayerCharacter>(FoundActors[0]);
    }

    return nullptr;
}

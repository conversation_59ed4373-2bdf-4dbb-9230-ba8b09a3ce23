#include "GA_Movement.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "../Characters/SurvivorsCharacterBase.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "AbilitySystemComponent.h"

UGA_Movement::UGA_Movement()
{
    // Set ability properties
    InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerActor;
    NetExecutionPolicy = EGameplayAbilityNetExecutionPolicy::LocalPredicted;
    
    // Set ability tags
    AbilityTags.AddTag(FSurvivorsGameplayTags::Get().Ability_Movement);
    
    // Block other movement abilities
    BlockAbilitiesWithTag.AddTag(FSurvivorsGameplayTags::Get().Ability_Movement);
    
    // Cancel when character is dead or stunned
    CancelAbilitiesWithTag.AddTag(FSurvivorsGameplayTags::Get().Character_State_Dead);
    CancelAbilitiesWithTag.AddTag(FSurvivorsGameplayTags::Get().Character_State_Stunned);
    
    // Activation blocked tags
    ActivationBlockedTags.AddTag(FSurvivorsGameplayTags::Get().Character_State_Dead);
    ActivationBlockedTags.AddTag(FSurvivorsGameplayTags::Get().Character_State_Stunned);
    
    // This ability should remain active
    bRetriggerInstancedAbility = true;
}

void UGA_Movement::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
    if (!CommitAbility(Handle, ActorInfo, ActivationInfo))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Cache character reference
    CachedCharacter = Cast<ACharacter>(ActorInfo->AvatarActor.Get());
    if (!CachedCharacter)
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Apply movement speed from attributes
    ApplyMovementSpeed();

    // Add movement state tag
    if (UAbilitySystemComponent* ASC = ActorInfo->AbilitySystemComponent.Get())
    {
        FGameplayTagContainer MovementTags;
        MovementTags.AddTag(FSurvivorsGameplayTags::Get().Character_State_Moving);
        ASC->AddLooseGameplayTags(MovementTags);
    }

    Super::ActivateAbility(Handle, ActorInfo, ActivationInfo, TriggerEventData);
}

void UGA_Movement::EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled)
{
    // Remove movement state tag
    if (UAbilitySystemComponent* ASC = ActorInfo->AbilitySystemComponent.Get())
    {
        FGameplayTagContainer MovementTags;
        MovementTags.AddTag(FSurvivorsGameplayTags::Get().Character_State_Moving);
        ASC->RemoveLooseGameplayTags(MovementTags);
    }

    // Clear cached character
    CachedCharacter = nullptr;

    Super::EndAbility(Handle, ActorInfo, ActivationInfo, bReplicateEndAbility, bWasCancelled);
}

bool UGA_Movement::CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags, const FGameplayTagContainer* TargetTags, FGameplayTagContainer* OptionalRelevantTags) const
{
    if (!Super::CanActivateAbility(Handle, ActorInfo, SourceTags, TargetTags, OptionalRelevantTags))
    {
        return false;
    }

    // Check if character can move
    return CanMove();
}

void UGA_Movement::ApplyMovementSpeed()
{
    if (!CachedCharacter)
    {
        return;
    }

    UCharacterMovementComponent* MovementComponent = CachedCharacter->GetCharacterMovement();
    if (!MovementComponent)
    {
        return;
    }

    // Get movement speed from attribute set
    float FinalMovementSpeed = BaseMovementSpeed * MovementSpeedMultiplier;

    if (ASurvivorsCharacterBase* SurvivorsCharacter = Cast<ASurvivorsCharacterBase>(CachedCharacter))
    {
        const float AttributeMovementSpeed = SurvivorsCharacter->GetMovementSpeed();
        if (AttributeMovementSpeed > 0.0f)
        {
            FinalMovementSpeed = AttributeMovementSpeed * MovementSpeedMultiplier;
        }
    }

    // Apply the movement speed
    MovementComponent->MaxWalkSpeed = FinalMovementSpeed;
}

bool UGA_Movement::CanMove() const
{
    if (!CachedCharacter)
    {
        return false;
    }

    // Check if character is alive
    if (ASurvivorsCharacterBase* SurvivorsCharacter = Cast<ASurvivorsCharacterBase>(CachedCharacter))
    {
        if (!SurvivorsCharacter->IsAlive())
        {
            return false;
        }
    }

    // Check movement component
    UCharacterMovementComponent* MovementComponent = CachedCharacter->GetCharacterMovement();
    if (!MovementComponent)
    {
        return false;
    }

    // Check if movement is enabled
    return MovementComponent->IsMovementMode(MOVE_Walking) || MovementComponent->IsMovementMode(MOVE_NavWalking);
}

#pragma once

#include "CoreMinimal.h"
#include "SurvivorsGameplayAbility.h"
#include "../SurvivorsTypes.h"
#include "GA_ProjectileWeapon.generated.h"

class ASurvivorsProjectile;

/**
 * UGA_ProjectileWeapon
 * 
 * Gameplay Ability for projectile-based weapons in the Survivors game.
 * Spawns projectiles that travel toward enemies and deal damage on impact.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API UGA_ProjectileWeapon : public USurvivorsGameplayAbility
{
    GENERATED_BODY()

public:
    UGA_ProjectileWeapon();

    // UGameplayAbility interface
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;
    virtual bool CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags = nullptr, const FGameplayTagContainer* TargetTags = nullptr, OUT FGameplayTagContainer* OptionalRelevantTags = nullptr) const override;

protected:
    // Projectile settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    TSubclassOf<ASurvivorsProjectile> ProjectileClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    float ProjectileSpeed = 1000.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    float ProjectileRange = 800.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    int32 ProjectileCount = 1;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    float ProjectileSpread = 0.0f;

    // Damage settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Damage")
    float BaseDamage = 15.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Damage")
    TSubclassOf<UGameplayEffect> DamageEffect;

    // Targeting
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Targeting")
    bool bAutoTarget = true;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Targeting")
    float AutoTargetRange = 600.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Targeting")
    bool bPiercing = false;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Targeting")
    int32 MaxPierceTargets = 3;

    // Visual/Audio effects
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    USoundBase* FireSound;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    UParticleSystem* MuzzleEffect;

    // Weapon functions
    UFUNCTION(BlueprintCallable, Category = "Projectile")
    void FireProjectiles();

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    ASurvivorsProjectile* SpawnProjectile(const FVector& SpawnLocation, const FVector& Direction);

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    TArray<AActor*> FindTargetsInRange() const;

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    FVector GetProjectileSpawnLocation() const;

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    FVector CalculateProjectileDirection(AActor* Target = nullptr) const;

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    void PlayFireEffects();

private:
    // Cached character reference
    UPROPERTY()
    TObjectPtr<class ASurvivorsCharacterBase> CachedCharacter;
};

#include "GA_ProjectileWeapon.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "../Characters/SurvivorsCharacterBase.h"
#include "../Characters/SurvivorsPlayerCharacter.h"
#include "../Projectiles/SurvivorsProjectile.h"
#include "AbilitySystemComponent.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Sound/SoundBase.h"
#include "Particles/ParticleSystem.h"

UGA_ProjectileWeapon::UGA_ProjectileWeapon()
{
    // Set ability properties
    InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerExecution;
    NetExecutionPolicy = EGameplayAbilityNetExecutionPolicy::ServerInitiated;
    
    // Set ability tags
    AbilityTags.AddTag(FSurvivorsGameplayTags::Get().Ability_Weapon_Projectile);
    
    // Activation owned tags
    ActivationOwnedTags.AddTag(FSurvivorsGameplayTags::Get().Character_State_Attacking);
    
    // Cancel when character is dead or stunned
    CancelAbilitiesWithTag.AddTag(FSurvivorsGameplayTags::Get().Character_State_Dead);
    CancelAbilitiesWithTag.AddTag(FSurvivorsGameplayTags::Get().Character_State_Stunned);
    
    // Activation blocked tags
    ActivationBlockedTags.AddTag(FSurvivorsGameplayTags::Get().Character_State_Dead);
    ActivationBlockedTags.AddTag(FSurvivorsGameplayTags::Get().Character_State_Stunned);
}

void UGA_ProjectileWeapon::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
    if (!HasAuthorityOrPredictionKey(ActorInfo, &ActivationInfo))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    if (!CommitAbility(Handle, ActorInfo, ActivationInfo))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Cache character reference
    CachedCharacter = Cast<ASurvivorsCharacterBase>(ActorInfo->AvatarActor.Get());
    if (!CachedCharacter)
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Fire projectiles
    FireProjectiles();

    // End ability immediately (this is a one-shot attack)
    EndAbility(Handle, ActorInfo, ActivationInfo, true, false);
}

void UGA_ProjectileWeapon::EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled)
{
    // Clear references
    CachedCharacter = nullptr;

    Super::EndAbility(Handle, ActorInfo, ActivationInfo, bReplicateEndAbility, bWasCancelled);
}

bool UGA_ProjectileWeapon::CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags, const FGameplayTagContainer* TargetTags, FGameplayTagContainer* OptionalRelevantTags) const
{
    if (!Super::CanActivateAbility(Handle, ActorInfo, SourceTags, TargetTags, OptionalRelevantTags))
    {
        return false;
    }

    // Check if character is alive
    if (ASurvivorsCharacterBase* Character = Cast<ASurvivorsCharacterBase>(ActorInfo->AvatarActor.Get()))
    {
        return Character->IsAlive();
    }

    return false;
}

void UGA_ProjectileWeapon::FireProjectiles()
{
    if (!CachedCharacter || !ProjectileClass)
    {
        return;
    }

    // Play fire effects
    PlayFireEffects();

    // Get spawn location
    FVector SpawnLocation = GetProjectileSpawnLocation();

    // Fire multiple projectiles if configured
    for (int32 i = 0; i < ProjectileCount; i++)
    {
        FVector Direction;
        
        if (bAutoTarget)
        {
            // Find targets and aim at them
            TArray<AActor*> Targets = FindTargetsInRange();
            AActor* Target = Targets.IsValidIndex(i) ? Targets[i] : (Targets.Num() > 0 ? Targets[0] : nullptr);
            Direction = CalculateProjectileDirection(Target);
        }
        else
        {
            // Fire in facing direction with spread
            Direction = CalculateProjectileDirection();
        }

        // Apply spread
        if (ProjectileSpread > 0.0f && ProjectileCount > 1)
        {
            float SpreadAngle = ProjectileSpread * (i - (ProjectileCount - 1) * 0.5f);
            FRotator SpreadRotation(0.0f, SpreadAngle, 0.0f);
            Direction = SpreadRotation.RotateVector(Direction);
        }

        // Spawn projectile
        ASurvivorsProjectile* Projectile = SpawnProjectile(SpawnLocation, Direction);
        if (Projectile)
        {
            // Configure projectile
            Projectile->SetDamage(BaseDamage);
            Projectile->SetRange(ProjectileRange);
            Projectile->SetSpeed(ProjectileSpeed);
            Projectile->SetPiercing(bPiercing, MaxPierceTargets);
            Projectile->SetDamageEffect(DamageEffect);
        }
    }
}

ASurvivorsProjectile* UGA_ProjectileWeapon::SpawnProjectile(const FVector& SpawnLocation, const FVector& Direction)
{
    if (!CachedCharacter || !ProjectileClass)
    {
        return nullptr;
    }

    FRotator SpawnRotation = Direction.Rotation();
    
    FActorSpawnParameters SpawnParams;
    SpawnParams.Owner = CachedCharacter;
    SpawnParams.Instigator = CachedCharacter;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;

    ASurvivorsProjectile* Projectile = CachedCharacter->GetWorld()->SpawnActor<ASurvivorsProjectile>(
        ProjectileClass, 
        SpawnLocation, 
        SpawnRotation, 
        SpawnParams
    );

    return Projectile;
}

TArray<AActor*> UGA_ProjectileWeapon::FindTargetsInRange() const
{
    TArray<AActor*> Targets;
    
    if (!CachedCharacter)
    {
        return Targets;
    }

    // For player characters, find enemies
    if (CachedCharacter->IsA<ASurvivorsPlayerCharacter>())
    {
        TArray<AActor*> FoundActors;
        UGameplayStatics::GetAllActorsOfClass(CachedCharacter->GetWorld(), APawn::StaticClass(), FoundActors);

        const FVector PlayerLocation = CachedCharacter->GetActorLocation();

        for (AActor* Actor : FoundActors)
        {
            if (Actor && Actor != CachedCharacter && !Actor->IsA<ASurvivorsPlayerCharacter>())
            {
                if (ASurvivorsCharacterBase* EnemyCharacter = Cast<ASurvivorsCharacterBase>(Actor))
                {
                    if (EnemyCharacter->IsAlive())
                    {
                        const float Distance = FVector::Dist(PlayerLocation, Actor->GetActorLocation());
                        if (Distance <= AutoTargetRange)
                        {
                            Targets.Add(Actor);
                        }
                    }
                }
            }
        }

        // Sort by distance
        Targets.Sort([PlayerLocation](const AActor& A, const AActor& B) {
            float DistA = FVector::Dist(PlayerLocation, A.GetActorLocation());
            float DistB = FVector::Dist(PlayerLocation, B.GetActorLocation());
            return DistA < DistB;
        });
    }

    return Targets;
}

FVector UGA_ProjectileWeapon::GetProjectileSpawnLocation() const
{
    if (!CachedCharacter)
    {
        return FVector::ZeroVector;
    }

    // Spawn slightly in front of character
    FVector ForwardVector = CachedCharacter->GetActorForwardVector();
    FVector SpawnLocation = CachedCharacter->GetActorLocation() + ForwardVector * 50.0f;
    SpawnLocation.Z += 50.0f; // Raise it up a bit

    return SpawnLocation;
}

FVector UGA_ProjectileWeapon::CalculateProjectileDirection(AActor* Target) const
{
    if (!CachedCharacter)
    {
        return FVector::ForwardVector;
    }

    if (Target)
    {
        // Aim at target
        FVector TargetLocation = Target->GetActorLocation();
        FVector SpawnLocation = GetProjectileSpawnLocation();
        return (TargetLocation - SpawnLocation).GetSafeNormal();
    }
    else
    {
        // Use character's facing direction
        return CachedCharacter->GetActorForwardVector();
    }
}

void UGA_ProjectileWeapon::PlayFireEffects()
{
    if (!CachedCharacter)
    {
        return;
    }

    // Play sound
    if (FireSound)
    {
        UGameplayStatics::PlaySoundAtLocation(CachedCharacter->GetWorld(), FireSound, CachedCharacter->GetActorLocation());
    }

    // Play muzzle effect
    if (MuzzleEffect)
    {
        FVector EffectLocation = GetProjectileSpawnLocation();
        UGameplayStatics::SpawnEmitterAtLocation(CachedCharacter->GetWorld(), MuzzleEffect, EffectLocation);
    }
}

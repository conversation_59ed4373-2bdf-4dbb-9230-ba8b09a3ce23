#pragma once

#include "CoreMinimal.h"
#include "SurvivorsGameplayAbility.h"
#include "../SurvivorsTypes.h"
#include "GA_AutoAttack.generated.h"

class UGameplayEffect;

/**
 * UGA_AutoAttack
 * 
 * Gameplay Ability for automatic attacking in the Survivors game.
 * Handles targeting, damage application, and attack timing with server authority.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API UGA_AutoAttack : public USurvivorsGameplayAbility
{
    GENERATED_BODY()

public:
    UGA_AutoAttack();

    // UGameplayAbility interface
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;
    virtual bool CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags = nullptr, const FGameplayTagContainer* TargetTags = nullptr, OUT FGameplayTagContainer* OptionalRelevantTags = nullptr) const override;

protected:
    // Attack settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Attack")
    float BaseDamage = 10.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Attack")
    float BaseAttackRange = 300.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Attack")
    float BaseAttackSpeed = 1.0f;

    // Damage effect to apply
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Attack")
    TSubclassOf<UGameplayEffect> DamageEffect;

    // Visual/Audio effects
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    TSubclassOf<AActor> ProjectileClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    USoundBase* AttackSound;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    UParticleSystem* AttackEffect;

    // Attack execution
    UFUNCTION(BlueprintCallable, Category = "Attack")
    void ExecuteAttack();

    UFUNCTION(BlueprintCallable, Category = "Attack")
    AActor* FindTarget() const;

    UFUNCTION(BlueprintCallable, Category = "Attack")
    bool IsValidTarget(AActor* Target) const;

    UFUNCTION(BlueprintCallable, Category = "Attack")
    void ApplyDamageToTarget(AActor* Target);

    UFUNCTION(BlueprintCallable, Category = "Attack")
    void SpawnProjectile(AActor* Target);

    UFUNCTION(BlueprintCallable, Category = "Attack")
    void PlayAttackEffects();

    // Get attack stats from attributes
    UFUNCTION(BlueprintCallable, Category = "Attack")
    float GetAttackDamage() const;

    UFUNCTION(BlueprintCallable, Category = "Attack")
    float GetAttackRange() const;

    UFUNCTION(BlueprintCallable, Category = "Attack")
    float GetAttackSpeed() const;

    UFUNCTION(BlueprintCallable, Category = "Attack")
    bool ShouldCriticalHit() const;

    UFUNCTION(BlueprintCallable, Category = "Attack")
    float GetCriticalMultiplier() const;

private:
    // Current target
    UPROPERTY()
    TObjectPtr<AActor> CurrentTarget;

    // Cached character reference
    UPROPERTY()
    TObjectPtr<class ASurvivorsCharacterBase> CachedCharacter;
};

#include "GA_EnemyRageMode.h"
#include "../Characters/SurvivorsCharacterBase.h"
#include "../GAS/SurvivorsAbilitySystemComponent.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "Engine/World.h"

UGA_EnemyRageMode::UGA_EnemyRageMode()
{
    AbilityInputID = EAbilityInputID::None;
    InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerActor;
    
    // Set ability tags
    AbilityTags.AddTag(FSurvivorsGameplayTags::Get().Ability_RageMode);
    
    // Set default values
    RageDuration = 10.0f;
    DamageMultiplier = 1.5f;
    SpeedMultiplier = 1.3f;
    HealthThreshold = 0.3f; // Activate when below 30% health
    RageCooldown = 30.0f;
}

void UGA_EnemyRageMode::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
    if (!CommitAbility(Handle, ActorInfo, ActivationInfo))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Get the character
    ASurvivorsCharacterBase* Character = GetSurvivorsCharacterFromActorInfo();
    if (!Character)
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Check if character is below health threshold
    if (!ShouldActivateRage(Character))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Start rage mode
    StartRageMode(Character);

    // Set timer for rage duration
    FTimerHandle RageTimer;
    GetWorld()->GetTimerManager().SetTimer(RageTimer, [this, Handle, ActorInfo, ActivationInfo]()
    {
        EndRageMode(Handle, ActorInfo, ActivationInfo);
    }, RageDuration, false);

    UE_LOG(LogTemp, Log, TEXT("Enemy entered rage mode"));
}

void UGA_EnemyRageMode::StartRageMode(ASurvivorsCharacterBase* Character)
{
    if (!Character)
    {
        return;
    }

    USurvivorsAbilitySystemComponent* ASC = GetSurvivorsAbilitySystemComponentFromActorInfo();
    if (!ASC)
    {
        return;
    }

    // Add rage mode tag
    ASC->AddLooseGameplayTag(FSurvivorsGameplayTags::Get().Ability_RageMode);

    // Apply rage mode effects
    if (RageModeEffect)
    {
        RageModeEffectHandle = ApplyEffectToSelf(RageModeEffect, GetAbilityLevel());
    }

    // Store original values for restoration
    USurvivorsAttributeSet* AttributeSet = GetSurvivorsAttributeSetFromActorInfo();
    if (AttributeSet)
    {
        OriginalAttackDamage = AttributeSet->GetAttackDamage();
        OriginalMovementSpeed = AttributeSet->GetMovementSpeed();
        OriginalAttackSpeed = AttributeSet->GetAttackSpeed();

        // Apply rage bonuses
        float NewDamage = OriginalAttackDamage * DamageMultiplier;
        float NewSpeed = OriginalMovementSpeed * SpeedMultiplier;
        float NewAttackSpeed = OriginalAttackSpeed * SpeedMultiplier;

        ASC->SetAttributeBaseValue(AttributeSet->GetAttackDamageAttribute(), NewDamage);
        ASC->SetAttributeBaseValue(AttributeSet->GetMovementSpeedAttribute(), NewSpeed);
        ASC->SetAttributeBaseValue(AttributeSet->GetAttackSpeedAttribute(), NewAttackSpeed);
    }

    // Visual/audio effects can be added here
    PlayRageEffects(Character);
}

void UGA_EnemyRageMode::EndRageMode(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo)
{
    ASurvivorsCharacterBase* Character = GetSurvivorsCharacterFromActorInfo();
    if (Character)
    {
        USurvivorsAbilitySystemComponent* ASC = GetSurvivorsAbilitySystemComponentFromActorInfo();
        if (ASC)
        {
            // Remove rage mode tag
            ASC->RemoveLooseGameplayTag(FSurvivorsGameplayTags::Get().Ability_RageMode);

            // Remove rage mode effect
            if (RageModeEffectHandle.IsValid())
            {
                ASC->RemoveActiveGameplayEffect(RageModeEffectHandle);
            }

            // Restore original attribute values
            USurvivorsAttributeSet* AttributeSet = GetSurvivorsAttributeSetFromActorInfo();
            if (AttributeSet)
            {
                ASC->SetAttributeBaseValue(AttributeSet->GetAttackDamageAttribute(), OriginalAttackDamage);
                ASC->SetAttributeBaseValue(AttributeSet->GetMovementSpeedAttribute(), OriginalMovementSpeed);
                ASC->SetAttributeBaseValue(AttributeSet->GetAttackSpeedAttribute(), OriginalAttackSpeed);
            }
        }

        // Stop visual/audio effects
        StopRageEffects(Character);
    }

    UE_LOG(LogTemp, Log, TEXT("Enemy rage mode ended"));

    // End ability
    EndAbility(Handle, ActorInfo, ActivationInfo, true, false);
}

bool UGA_EnemyRageMode::ShouldActivateRage(ASurvivorsCharacterBase* Character) const
{
    if (!Character)
    {
        return false;
    }

    // Check health threshold
    float HealthPercent = Character->GetHealthPercent();
    if (HealthPercent > HealthThreshold)
    {
        return false;
    }

    // Check if already in rage mode
    USurvivorsAbilitySystemComponent* ASC = GetSurvivorsAbilitySystemComponentFromActorInfo();
    if (ASC && ASC->HasMatchingGameplayTag(FSurvivorsGameplayTags::Get().Ability_RageMode))
    {
        return false;
    }

    return true;
}

bool UGA_EnemyRageMode::CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags, const FGameplayTagContainer* TargetTags, FGameplayTagContainer* OptionalRelevantTags) const
{
    if (!Super::CanActivateAbility(Handle, ActorInfo, SourceTags, TargetTags, OptionalRelevantTags))
    {
        return false;
    }

    // Check if character should activate rage
    if (ASurvivorsCharacterBase* Character = Cast<ASurvivorsCharacterBase>(ActorInfo->AvatarActor.Get()))
    {
        return ShouldActivateRage(Character);
    }

    return false;
}

void UGA_EnemyRageMode::PlayRageEffects(ASurvivorsCharacterBase* Character)
{
    if (!Character)
    {
        return;
    }

    // TODO: Add visual effects like red glow, particle systems, etc.
    // TODO: Add audio effects like roar sound
    
    UE_LOG(LogTemp, Log, TEXT("Playing rage mode effects"));
}

void UGA_EnemyRageMode::StopRageEffects(ASurvivorsCharacterBase* Character)
{
    if (!Character)
    {
        return;
    }

    // TODO: Stop visual and audio effects
    
    UE_LOG(LogTemp, Log, TEXT("Stopping rage mode effects"));
}

FActiveGameplayEffectHandle UGA_EnemyRageMode::ApplyEffectToSelf(TSubclassOf<UGameplayEffect> EffectClass, float Level) const
{
    if (!EffectClass)
    {
        return FActiveGameplayEffectHandle();
    }

    if (USurvivorsAbilitySystemComponent* ASC = GetSurvivorsAbilitySystemComponentFromActorInfo())
    {
        return ASC->ApplyGameplayEffectToSelf(EffectClass, Level);
    }

    return FActiveGameplayEffectHandle();
}

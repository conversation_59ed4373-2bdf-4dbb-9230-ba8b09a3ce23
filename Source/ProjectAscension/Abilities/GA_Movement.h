#pragma once

#include "CoreMinimal.h"
#include "Abilities/GameplayAbility.h"
#include "GA_Movement.generated.h"

/**
 * UGA_Movement
 * 
 * Gameplay Ability for character movement in the Survivors game.
 * Handles WASD movement with enhanced input integration and server authority.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API UGA_Movement : public UGameplayAbility
{
    GENERATED_BODY()

public:
    UGA_Movement();

    // UGameplayAbility interface
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;
    virtual bool CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags = nullptr, const FGameplayTagContainer* TargetTags = nullptr, OUT FGameplayTagContainer* OptionalRelevantTags = nullptr) const override;

protected:
    // Movement settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Movement")
    float BaseMovementSpeed = 300.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Movement")
    float MovementSpeedMultiplier = 1.0f;

    // Apply movement speed from attributes
    UFUNCTION(BlueprintCallable, Category = "Movement")
    void ApplyMovementSpeed();

    // Check if character can move
    UFUNCTION(BlueprintCallable, Category = "Movement")
    bool CanMove() const;

private:
    // Cache the character reference
    UPROPERTY()
    TObjectPtr<ACharacter> CachedCharacter;
};

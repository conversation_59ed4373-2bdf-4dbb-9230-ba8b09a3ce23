#pragma once

#include "CoreMinimal.h"
#include "SurvivorsGameplayAbility.h"
#include "../SurvivorsTypes.h"
#include "GA_EnemyDash.generated.h"

class ASurvivorsCharacterBase;
class ASurvivorsPlayerCharacter;

/**
 * UGA_EnemyDash
 * 
 * Enemy dash ability that allows enemies to quickly dash towards the player.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API UGA_EnemyDash : public USurvivorsGameplayAbility
{
    GENERATED_BODY()

public:
    UGA_EnemyDash();

    // UGameplayAbility interface
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;

protected:
    // Dash properties
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash")
    float DashDistance = 600.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash")
    float DashDuration = 0.3f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash")
    float DashDamage = 30.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash")
    float DashCooldown = 3.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash")
    float DashRadius = 100.0f;

    // Damage effect to apply
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Dash")
    TSubclassOf<UGameplayEffect> DashDamageEffect;

private:
    // Dash state
    FVector DashStartLocation;
    FVector DashTargetLocation;
    float DashElapsedTime = 0.0f;
    FTimerHandle DashUpdateTimer;

    // Dash functions
    void StartDash(ASurvivorsCharacterBase* Character);
    void UpdateDashMovement(ASurvivorsCharacterBase* Character);
    void CheckDashCollision(ASurvivorsCharacterBase* Character);
    void ApplyDashDamage(ASurvivorsPlayerCharacter* Player);
    void CompleteDash(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo);

    // Helper functions
    ASurvivorsPlayerCharacter* FindPlayerTarget() const;
};

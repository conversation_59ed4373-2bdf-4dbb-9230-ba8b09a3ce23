#pragma once

#include "CoreMinimal.h"
#include "SurvivorsGameplayAbility.h"
#include "../SurvivorsTypes.h"
#include "GA_EnemyRageMode.generated.h"

class ASurvivorsCharacterBase;

/**
 * UGA_EnemyRageMode
 * 
 * Enemy rage mode ability that increases damage and speed when health is low.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API UGA_EnemyRageMode : public USurvivorsGameplayAbility
{
    GENERATED_BODY()

public:
    UGA_EnemyRageMode();

    // UGameplayAbility interface
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    virtual bool CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags = nullptr, const FGameplayTagContainer* TargetTags = nullptr, FGameplayTagContainer* OptionalRelevantTags = nullptr) const override;

protected:
    // Rage properties
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Rage")
    float RageDuration = 10.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Rage")
    float DamageMultiplier = 1.5f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Rage")
    float SpeedMultiplier = 1.3f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Rage")
    float HealthThreshold = 0.3f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Rage")
    float RageCooldown = 30.0f;

    // Rage mode effect
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Rage")
    TSubclassOf<UGameplayEffect> RageModeEffect;

private:
    // Rage state
    FActiveGameplayEffectHandle RageModeEffectHandle;
    float OriginalAttackDamage = 0.0f;
    float OriginalMovementSpeed = 0.0f;
    float OriginalAttackSpeed = 0.0f;

    // Rage functions
    void StartRageMode(ASurvivorsCharacterBase* Character);
    void EndRageMode(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo);
    bool ShouldActivateRage(ASurvivorsCharacterBase* Character) const;

    // Effects
    void PlayRageEffects(ASurvivorsCharacterBase* Character);
    void StopRageEffects(ASurvivorsCharacterBase* Character);

    // Helper function
    FActiveGameplayEffectHandle ApplyEffectToSelf(TSubclassOf<UGameplayEffect> EffectClass, float Level = 1.0f) const;
};

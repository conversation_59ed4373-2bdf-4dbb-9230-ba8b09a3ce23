#pragma once

#include "CoreMinimal.h"
#include "SurvivorsGameplayAbility.h"
#include "../SurvivorsTypes.h"
#include "GA_EnemyCharge.generated.h"

class ASurvivorsCharacterBase;
class ASurvivorsPlayerCharacter;

/**
 * UGA_EnemyCharge
 * 
 * Enemy charge ability that allows enemies to charge at the player with increased speed.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API UGA_EnemyCharge : public USurvivorsGameplayAbility
{
    GENERATED_BODY()

public:
    UGA_EnemyCharge();

    // UGameplayAbility interface
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;

protected:
    // Charge properties
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Charge")
    float ChargeDistance = 800.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Charge")
    float ChargeDuration = 1.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Charge")
    float ChargeDamage = 50.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Charge")
    float ChargeRadius = 150.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Charge")
    float ChargeCooldown = 5.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Charge")
    float ChargeSpeed = 1200.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Charge")
    float KnockbackStrength = 500.0f;

    // Damage effect to apply
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Charge")
    TSubclassOf<UGameplayEffect> ChargeDamageEffect;

private:
    // Charge state
    FVector ChargeStartLocation;
    FVector ChargeTargetLocation;
    FVector ChargeDirection;
    float ChargeElapsedTime = 0.0f;
    bool bHasHitTarget = false;
    float OriginalMaxWalkSpeed = 0.0f;
    FTimerHandle ChargeUpdateTimer;

    // Charge functions
    void StartCharge(ASurvivorsCharacterBase* Character);
    void UpdateChargeMovement(ASurvivorsCharacterBase* Character);
    void CheckChargeCollision(ASurvivorsCharacterBase* Character);
    void ApplyChargeDamage(ASurvivorsPlayerCharacter* Player);
    void ApplyKnockback(ASurvivorsPlayerCharacter* Player, ASurvivorsCharacterBase* Charger);
    void CompleteCharge(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo);

    // Helper functions
    ASurvivorsPlayerCharacter* FindPlayerTarget() const;
};

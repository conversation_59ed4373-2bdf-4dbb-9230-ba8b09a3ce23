#include "GA_EnemyDash.h"
#include "../Characters/SurvivorsCharacterBase.h"
#include "../Characters/SurvivorsPlayerCharacter.h"
#include "../GAS/SurvivorsAbilitySystemComponent.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"

UGA_EnemyDash::UGA_EnemyDash()
{
    AbilityInputID = EAbilityInputID::None;
    InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerActor;
    
    // Set ability tags
    AbilityTags.AddTag(FSurvivorsGameplayTags::Get().Ability_Dash);
    
    // Set default values
    DashDistance = 600.0f;
    DashDuration = 0.3f;
    DashDamage = 30.0f;
    DashCooldown = 3.0f;
    DashRadius = 100.0f;
}

void UGA_EnemyDash::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
    if (!CommitAbility(Handle, ActorInfo, ActivationInfo))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Get the character
    ASurvivorsCharacterBase* Character = GetSurvivorsCharacterFromActorInfo();
    if (!Character)
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Find target (player)
    ASurvivorsPlayerCharacter* Player = FindPlayerTarget();
    if (!Player)
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Calculate dash direction
    FVector DashDirection = (Player->GetActorLocation() - Character->GetActorLocation()).GetSafeNormal();
    FVector DashTarget = Character->GetActorLocation() + (DashDirection * DashDistance);

    // Store dash info
    DashStartLocation = Character->GetActorLocation();
    DashTargetLocation = DashTarget;
    DashElapsedTime = 0.0f;

    // Start dash
    StartDash(Character);

    // Set timer for dash completion
    FTimerHandle DashTimer;
    GetWorld()->GetTimerManager().SetTimer(DashTimer, [this, Handle, ActorInfo, ActivationInfo]()
    {
        CompleteDash(Handle, ActorInfo, ActivationInfo);
    }, DashDuration, false);
}

void UGA_EnemyDash::StartDash(ASurvivorsCharacterBase* Character)
{
    if (!Character)
    {
        return;
    }

    // Disable gravity and collision during dash
    if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
    {
        MovementComp->SetMovementMode(MOVE_Flying);
        MovementComp->GravityScale = 0.0f;
    }

    // Add dash tag
    if (USurvivorsAbilitySystemComponent* ASC = GetSurvivorsAbilitySystemComponentFromActorInfo())
    {
        ASC->AddLooseGameplayTag(FSurvivorsGameplayTags::Get().Character_State_Moving);
    }

    // Start dash movement
    GetWorld()->GetTimerManager().SetTimer(DashUpdateTimer, [this, Character]()
    {
        UpdateDashMovement(Character);
    }, 0.016f, true); // ~60 FPS updates
}

void UGA_EnemyDash::UpdateDashMovement(ASurvivorsCharacterBase* Character)
{
    if (!Character)
    {
        return;
    }

    DashElapsedTime += 0.016f;
    float DashProgress = FMath::Clamp(DashElapsedTime / DashDuration, 0.0f, 1.0f);

    // Interpolate position
    FVector CurrentLocation = FMath::Lerp(DashStartLocation, DashTargetLocation, DashProgress);
    Character->SetActorLocation(CurrentLocation);

    // Check for collision with player during dash
    CheckDashCollision(Character);
}

void UGA_EnemyDash::CheckDashCollision(ASurvivorsCharacterBase* Character)
{
    if (!Character)
    {
        return;
    }

    // Find nearby players
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ASurvivorsPlayerCharacter::StaticClass(), FoundActors);

    for (AActor* Actor : FoundActors)
    {
        if (ASurvivorsPlayerCharacter* Player = Cast<ASurvivorsPlayerCharacter>(Actor))
        {
            float Distance = FVector::Dist(Character->GetActorLocation(), Player->GetActorLocation());
            if (Distance <= DashRadius)
            {
                // Apply damage to player
                ApplyDashDamage(Player);
            }
        }
    }
}

void UGA_EnemyDash::ApplyDashDamage(ASurvivorsPlayerCharacter* Player)
{
    if (!Player || !DashDamageEffect)
    {
        return;
    }

    if (USurvivorsAbilitySystemComponent* PlayerASC = Cast<USurvivorsAbilitySystemComponent>(Player->GetAbilitySystemComponent()))
    {
        ApplyEffectToTarget(DashDamageEffect, PlayerASC, GetAbilityLevel());
        
        UE_LOG(LogTemp, Log, TEXT("Enemy dash hit player for %f damage"), DashDamage);
    }
}

void UGA_EnemyDash::CompleteDash(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo)
{
    ASurvivorsCharacterBase* Character = GetSurvivorsCharacterFromActorInfo();
    if (Character)
    {
        // Restore normal movement
        if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
        {
            MovementComp->SetMovementMode(MOVE_Walking);
            MovementComp->GravityScale = 1.0f;
        }

        // Remove dash tag
        if (USurvivorsAbilitySystemComponent* ASC = GetSurvivorsAbilitySystemComponentFromActorInfo())
        {
            ASC->RemoveLooseGameplayTag(FSurvivorsGameplayTags::Get().Character_State_Moving);
        }
    }

    // Clear dash timer
    GetWorld()->GetTimerManager().ClearTimer(DashUpdateTimer);

    // End ability
    EndAbility(Handle, ActorInfo, ActivationInfo, true, false);
}

ASurvivorsPlayerCharacter* UGA_EnemyDash::FindPlayerTarget() const
{
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ASurvivorsPlayerCharacter::StaticClass(), FoundActors);

    if (FoundActors.Num() > 0)
    {
        return Cast<ASurvivorsPlayerCharacter>(FoundActors[0]);
    }

    return nullptr;
}

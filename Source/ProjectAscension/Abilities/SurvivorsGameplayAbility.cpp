#include "SurvivorsGameplayAbility.h"
#include "../Characters/SurvivorsCharacterBase.h"
#include "../GAS/SurvivorsAbilitySystemComponent.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "../GAS/SurvivorsGameplayTags.h"

USurvivorsGameplayAbility::USurvivorsGameplayAbility()
{
    // Set default values
    InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerActor;
    NetExecutionPolicy = EGameplayAbilityNetExecutionPolicy::LocalPredicted;
    bRetriggerInstancedAbility = true;
}

void USurvivorsGameplayAbility::OnAvatarSet(const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilitySpec& Spec)
{
    Super::OnAvatarSet(ActorInfo, Spec);

    if (bActivateAbilityOnGranted)
    {
        ActorInfo->AbilitySystemComponent->TryActivateAbility(Spec.Handle, false);
    }
}

ASurvivorsCharacterBase* USurvivorsGameplayAbility::GetSurvivorsCharacterFromActorInfo() const
{
    return Cast<ASurvivorsCharacterBase>(GetAvatarActorFromActorInfo());
}

USurvivorsAbilitySystemComponent* USurvivorsGameplayAbility::GetSurvivorsAbilitySystemComponentFromActorInfo() const
{
    return Cast<USurvivorsAbilitySystemComponent>(GetAbilitySystemComponentFromActorInfo());
}

USurvivorsAttributeSet* USurvivorsGameplayAbility::GetSurvivorsAttributeSetFromActorInfo() const
{
    if (USurvivorsAbilitySystemComponent* SurvivorsASC = GetSurvivorsAbilitySystemComponentFromActorInfo())
    {
        return SurvivorsASC->GetSurvivorsAttributeSet();
    }
    return nullptr;
}

bool USurvivorsGameplayAbility::CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags, const FGameplayTagContainer* TargetTags, FGameplayTagContainer* OptionalRelevantTags) const
{
    if (!Super::CanActivateAbility(Handle, ActorInfo, SourceTags, TargetTags, OptionalRelevantTags))
    {
        return false;
    }

    // Check if character is alive
    if (ASurvivorsCharacterBase* Character = Cast<ASurvivorsCharacterBase>(ActorInfo->AvatarActor.Get()))
    {
        return Character->IsAlive();
    }

    return false;
}

void USurvivorsGameplayAbility::ApplyEffectToSelf(TSubclassOf<UGameplayEffect> EffectClass, float Level) const
{
    if (!EffectClass)
    {
        return;
    }

    if (USurvivorsAbilitySystemComponent* ASC = GetSurvivorsAbilitySystemComponentFromActorInfo())
    {
        ASC->ApplyGameplayEffectToSelf(EffectClass, Level);
    }
}

void USurvivorsGameplayAbility::ApplyEffectToTarget(TSubclassOf<UGameplayEffect> EffectClass, UAbilitySystemComponent* TargetASC, float Level) const
{
    if (!EffectClass || !TargetASC)
    {
        return;
    }

    if (USurvivorsAbilitySystemComponent* ASC = GetSurvivorsAbilitySystemComponentFromActorInfo())
    {
        ASC->ApplyGameplayEffectToTarget(EffectClass, TargetASC, Level);
    }
}

float USurvivorsGameplayAbility::GetAbilityLevel() const
{
    return GetAbilityLevel(GetCurrentAbilitySpecHandle(), GetCurrentActorInfo());
}

float USurvivorsGameplayAbility::GetAbilityLevel(FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo) const
{
    if (ActorInfo && Handle.IsValid())
    {
        if (const FGameplayAbilitySpec* AbilitySpec = ActorInfo->AbilitySystemComponent->FindAbilitySpecFromHandle(Handle))
        {
            return AbilitySpec->Level;
        }
    }
    return 1.0f;
}

FGameplayEffectContextHandle USurvivorsGameplayAbility::MakeEffectContext(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo) const
{
    FGameplayEffectContextHandle ContextHandle = Super::MakeEffectContext(Handle, ActorInfo);
    
    // Add any custom context data here
    
    return ContextHandle;
}

#include "GA_AutoAttack.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "../Characters/SurvivorsCharacterBase.h"
#include "../Characters/SurvivorsPlayerCharacter.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Sound/SoundBase.h"
#include "Particles/ParticleSystem.h"

UGA_AutoAttack::UGA_AutoAttack()
{
    // Set ability properties
    InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerExecution;
    NetExecutionPolicy = EGameplayAbilityNetExecutionPolicy::ServerInitiated;
    
    // Set ability tags
    AbilityTags.AddTag(FSurvivorsGameplayTags::Get().Ability_AutoAttack);
    
    // Activation owned tags
    ActivationOwnedTags.AddTag(FSurvivorsGameplayTags::Get().Character_State_Attacking);
    
    // Cancel when character is dead or stunned
    CancelAbilitiesWithTag.AddTag(FSurvivorsGameplayTags::Get().Character_State_Dead);
    CancelAbilitiesWithTag.AddTag(FSurvivorsGameplayTags::Get().Character_State_Stunned);
    
    // Activation blocked tags
    ActivationBlockedTags.AddTag(FSurvivorsGameplayTags::Get().Character_State_Dead);
    ActivationBlockedTags.AddTag(FSurvivorsGameplayTags::Get().Character_State_Stunned);
}

void UGA_AutoAttack::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
    if (!HasAuthorityOrPredictionKey(ActorInfo, &ActivationInfo))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    if (!CommitAbility(Handle, ActorInfo, ActivationInfo))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Cache character reference
    CachedCharacter = Cast<ASurvivorsCharacterBase>(ActorInfo->AvatarActor.Get());
    if (!CachedCharacter)
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Execute the attack
    ExecuteAttack();

    // End ability immediately (this is a one-shot attack)
    EndAbility(Handle, ActorInfo, ActivationInfo, true, false);
}

void UGA_AutoAttack::EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled)
{
    // Clear references
    CurrentTarget = nullptr;
    CachedCharacter = nullptr;

    Super::EndAbility(Handle, ActorInfo, ActivationInfo, bReplicateEndAbility, bWasCancelled);
}

bool UGA_AutoAttack::CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags, const FGameplayTagContainer* TargetTags, FGameplayTagContainer* OptionalRelevantTags) const
{
    if (!Super::CanActivateAbility(Handle, ActorInfo, SourceTags, TargetTags, OptionalRelevantTags))
    {
        return false;
    }

    // Check if character is alive
    if (ASurvivorsCharacterBase* Character = Cast<ASurvivorsCharacterBase>(ActorInfo->AvatarActor.Get()))
    {
        return Character->IsAlive();
    }

    return false;
}

void UGA_AutoAttack::ExecuteAttack()
{
    if (!CachedCharacter)
    {
        return;
    }

    // Find target
    CurrentTarget = FindTarget();
    if (!CurrentTarget || !IsValidTarget(CurrentTarget))
    {
        return;
    }

    // Play attack effects
    PlayAttackEffects();

    // Apply damage
    ApplyDamageToTarget(CurrentTarget);

    // Spawn projectile if configured
    if (ProjectileClass)
    {
        SpawnProjectile(CurrentTarget);
    }
}

AActor* UGA_AutoAttack::FindTarget() const
{
    if (!CachedCharacter)
    {
        return nullptr;
    }

    // For player characters, use their target finding logic
    if (ASurvivorsPlayerCharacter* PlayerCharacter = Cast<ASurvivorsPlayerCharacter>(CachedCharacter))
    {
        return PlayerCharacter->FindNearestEnemy();
    }

    // For AI characters, find nearest player
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(CachedCharacter->GetWorld(), ASurvivorsPlayerCharacter::StaticClass(), FoundActors);

    AActor* NearestTarget = nullptr;
    float NearestDistance = FLT_MAX;
    const FVector CharacterLocation = CachedCharacter->GetActorLocation();
    const float AttackRange = GetAttackRange();

    for (AActor* Actor : FoundActors)
    {
        if (Actor && Actor != CachedCharacter)
        {
            const float Distance = FVector::Dist(CharacterLocation, Actor->GetActorLocation());
            if (Distance < NearestDistance && Distance <= AttackRange)
            {
                NearestDistance = Distance;
                NearestTarget = Actor;
            }
        }
    }

    return NearestTarget;
}

bool UGA_AutoAttack::IsValidTarget(AActor* Target) const
{
    if (!Target || !CachedCharacter)
    {
        return false;
    }

    // Check if target is alive
    if (ASurvivorsCharacterBase* TargetCharacter = Cast<ASurvivorsCharacterBase>(Target))
    {
        if (!TargetCharacter->IsAlive())
        {
            return false;
        }
    }

    // Check range
    const float Distance = FVector::Dist(CachedCharacter->GetActorLocation(), Target->GetActorLocation());
    return Distance <= GetAttackRange();
}

void UGA_AutoAttack::ApplyDamageToTarget(AActor* Target)
{
    if (!Target || !CachedCharacter || !DamageEffect)
    {
        return;
    }

    UAbilitySystemComponent* TargetASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Target);
    if (!TargetASC)
    {
        return;
    }

    // Create damage effect spec
    FGameplayEffectContextHandle EffectContext = GetAbilitySystemComponentFromActorInfo()->MakeEffectContext();
    EffectContext.AddSourceObject(CachedCharacter);
    EffectContext.AddInstigator(CachedCharacter, CachedCharacter);

    FGameplayEffectSpecHandle DamageSpecHandle = GetAbilitySystemComponentFromActorInfo()->MakeOutgoingSpec(DamageEffect, GetAbilityLevel(), EffectContext);
    if (DamageSpecHandle.IsValid())
    {
        // Calculate final damage
        float FinalDamage = GetAttackDamage();
        
        // Apply critical hit
        if (ShouldCriticalHit())
        {
            FinalDamage *= GetCriticalMultiplier();
        }

        // Set damage magnitude
        DamageSpecHandle.Data->SetSetByCallerMagnitude(FSurvivorsGameplayTags::Get().Effect_Damage, FinalDamage);

        // Apply the damage effect
        GetAbilitySystemComponentFromActorInfo()->ApplyGameplayEffectSpecToTarget(*DamageSpecHandle.Data.Get(), TargetASC);
    }
}

void UGA_AutoAttack::SpawnProjectile(AActor* Target)
{
    if (!ProjectileClass || !CachedCharacter || !Target)
    {
        return;
    }

    // Spawn projectile at character location
    FVector SpawnLocation = CachedCharacter->GetActorLocation();
    FRotator SpawnRotation = (Target->GetActorLocation() - SpawnLocation).Rotation();

    FActorSpawnParameters SpawnParams;
    SpawnParams.Owner = CachedCharacter;
    SpawnParams.Instigator = CachedCharacter;

    AActor* Projectile = CachedCharacter->GetWorld()->SpawnActor<AActor>(ProjectileClass, SpawnLocation, SpawnRotation, SpawnParams);
    
    // TODO: Set projectile target or direction
}

void UGA_AutoAttack::PlayAttackEffects()
{
    if (!CachedCharacter)
    {
        return;
    }

    // Play sound
    if (AttackSound)
    {
        UGameplayStatics::PlaySoundAtLocation(CachedCharacter->GetWorld(), AttackSound, CachedCharacter->GetActorLocation());
    }

    // Play particle effect
    if (AttackEffect)
    {
        UGameplayStatics::SpawnEmitterAtLocation(CachedCharacter->GetWorld(), AttackEffect, CachedCharacter->GetActorLocation());
    }
}

// Attribute getters
float UGA_AutoAttack::GetAttackDamage() const
{
    float Damage = BaseDamage;
    if (CachedCharacter)
    {
        const float AttributeDamage = CachedCharacter->GetAttackDamage();
        if (AttributeDamage > 0.0f)
        {
            Damage = AttributeDamage;
        }
    }
    return Damage;
}

float UGA_AutoAttack::GetAttackRange() const
{
    float Range = BaseAttackRange;
    if (CachedCharacter)
    {
        const float AttributeRange = CachedCharacter->GetAttackRange();
        if (AttributeRange > 0.0f)
        {
            Range = AttributeRange;
        }
    }
    return Range;
}

float UGA_AutoAttack::GetAttackSpeed() const
{
    float Speed = BaseAttackSpeed;
    if (CachedCharacter)
    {
        const float AttributeSpeed = CachedCharacter->GetAttackSpeed();
        if (AttributeSpeed > 0.0f)
        {
            Speed = AttributeSpeed;
        }
    }
    return Speed;
}

bool UGA_AutoAttack::ShouldCriticalHit() const
{
    if (!CachedCharacter || !CachedCharacter->GetAttributeSet())
    {
        return false;
    }

    const float CritChance = CachedCharacter->GetAttributeSet()->GetCriticalChance();
    const float RandomValue = FMath::RandRange(0.0f, 1.0f);
    
    return RandomValue <= CritChance;
}

float UGA_AutoAttack::GetCriticalMultiplier() const
{
    if (!CachedCharacter || !CachedCharacter->GetAttributeSet())
    {
        return 2.0f;
    }

    return CachedCharacter->GetAttributeSet()->GetCriticalMultiplier();
}

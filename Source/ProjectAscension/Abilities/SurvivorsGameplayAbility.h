#pragma once

#include "CoreMinimal.h"
#include "Abilities/GameplayAbility.h"
#include "../SurvivorsTypes.h"
#include "SurvivorsGameplayAbility.generated.h"

class ASurvivorsCharacterBase;
class USurvivorsAbilitySystemComponent;
class USurvivorsAttributeSet;

/**
 * USurvivorsGameplayAbility
 * 
 * Base class for all gameplay abilities in the Survivors game.
 * Provides common functionality and helper methods.
 */
UCLASS(Abstract, BlueprintType, Blueprintable)
class PROJECTASCENSION_API USurvivorsGameplayAbility : public UGameplayAbility
{
    GENERATED_BODY()

public:
    USurvivorsGameplayAbility();

    // UGameplayAbility interface
    virtual void OnAvatarSet(const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilitySpec& Spec) override;
    virtual bool CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags = nullptr, const FGameplayTagContainer* TargetTags = nullptr, FGameplayTagContainer* OptionalRelevantTags = nullptr) const override;

    // Helper functions
    UFUNCTION(BlueprintCallable, Category = "Survivors|Ability")
    ASurvivorsCharacterBase* GetSurvivorsCharacterFromActorInfo() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Ability")
    USurvivorsAbilitySystemComponent* GetSurvivorsAbilitySystemComponentFromActorInfo() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Ability")
    USurvivorsAttributeSet* GetSurvivorsAttributeSetFromActorInfo() const;

    // Effect application helpers
    UFUNCTION(BlueprintCallable, Category = "Survivors|Ability")
    void ApplyEffectToSelf(TSubclassOf<UGameplayEffect> EffectClass, float Level = 1.0f) const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Ability")
    void ApplyEffectToTarget(TSubclassOf<UGameplayEffect> EffectClass, UAbilitySystemComponent* TargetASC, float Level = 1.0f) const;

    // Ability level helpers
    UFUNCTION(BlueprintCallable, Category = "Survivors|Ability")
    float GetAbilityLevel() const;

    // Note: This function cannot be BlueprintCallable because FGameplayAbilityActorInfo* cannot be exposed to Blueprint
    float GetAbilityLevel(FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo) const;

    // Effect context
    virtual FGameplayEffectContextHandle MakeEffectContext(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo) const override;

protected:
    // Ability input ID
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Ability")
    EAbilityInputID AbilityInputID = EAbilityInputID::None;

    // Auto-activate when granted
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Ability")
    bool bActivateAbilityOnGranted = false;
};

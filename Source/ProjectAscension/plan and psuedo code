// =============================================================================
// GAS-INTEGRATED ITEM SYSTEM FOR UNREAL ENGINE
// =============================================================================

#pragma once

#include "CoreMinimal.h"
#include "GameplayAbilitySpec.h"
#include "GameplayEffectSpec.h"
#include "AttributeSet.h"
#include "GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Components/ActorComponent.h"
#include "ItemSystem.generated.h"

// Forward declarations
class UAbilitySystemComponent;
class UGameplayEffect;
class UGameplayAbility;

// =============================================================================
// ITEM COMPONENT INTERFACE
// =============================================================================

UCLASS(BlueprintType, Blueprintable, Abstract)
class GAMEPROJECT_API UItemComponent : public UObject
{
    GENERATED_BODY()

public:
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Component")
    FGameplayTag ComponentType;

    // Virtual functions for component lifecycle
    virtual bool Validate() const { return true; }
    virtual void ApplyToASC(UAbilitySystemComponent* ASC) {}
    virtual void RemoveFromASC(UAbilitySystemComponent* ASC) {}
    virtual void SerializeComponent(FArchive& Ar) {}
    
    // GAS Integration helpers
    UFUNCTION(BlueprintImplementableEvent, Category = "GAS")
    void OnAppliedToASC(UAbilitySystemComponent* ASC);
    
    UFUNCTION(BlueprintImplementableEvent, Category = "GAS")
    void OnRemovedFromASC(UAbilitySystemComponent* ASC);
};

// =============================================================================
// BASE ITEM CLASS
// =============================================================================

UCLASS(BlueprintType, Blueprintable)
class GAMEPROJECT_API UItemObject : public UObject
{
    GENERATED_BODY()

public:
    UItemObject();

protected:
    // Component storage
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TMap<FGameplayTag, UItemComponent*> Components;

    // Cached GAS references
    UPROPERTY()
    TWeakObjectPtr<UAbilitySystemComponent> CachedASC;

    // Applied effects/abilities tracking
    UPROPERTY()
    TArray<FActiveGameplayEffectHandle> AppliedEffects;
    
    UPROPERTY()
    TArray<FGameplayAbilitySpecHandle> GrantedAbilities;

public:
    // Component management
    UFUNCTION(BlueprintCallable, Category = "Item")
    void AddComponent(UItemComponent* Component);
    
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Item")
    UItemComponent* GetComponent(FGameplayTag ComponentType) const;
    
    template<class T>
    T* GetComponent(FGameplayTag ComponentType) const
    {
        return Cast<T>(GetComponent(ComponentType));
    }
    
    UFUNCTION(BlueprintCallable, Category = "Item")
    bool RemoveComponent(FGameplayTag ComponentType);
    
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Item")
    bool HasComponent(FGameplayTag ComponentType) const;

    // GAS Integration
    UFUNCTION(BlueprintCallable, Category = "GAS")
    void ApplyToAbilitySystem(UAbilitySystemComponent* ASC);
    
    UFUNCTION(BlueprintCallable, Category = "GAS")
    void RemoveFromAbilitySystem();
    
    // Validation & Serialization
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Item")
    bool IsValid() const;
    
    virtual void Serialize(FArchive& Ar) override;
};

// =============================================================================
// BASIC INFO COMPONENT
// =============================================================================

UENUM(BlueprintType)
enum class EItemRarity : uint8
{
    Normal,
    Magic,
    Rare,
    Unique
};

UCLASS(BlueprintType, Blueprintable)
class GAMEPROJECT_API UBasicInfoComponent : public UItemComponent
{
    GENERATED_BODY()

public:
    UBasicInfoComponent();

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    FString ItemName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    int32 ItemLevel;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    EItemRarity Rarity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    FString BaseType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Basic Info")
    TArray<FString> ImplicitMods;

    // Base item attributes (damage, armor, etc.)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Base Stats")
    TMap<FGameplayTag, float> BaseAttributes;

    virtual bool Validate() const override;
    virtual void ApplyToASC(UAbilitySystemComponent* ASC) override;
};

// =============================================================================
// AFFIX COMPONENT (GAS-POWERED)
// =============================================================================

USTRUCT(BlueprintType)
struct FItemAffix
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString AffixID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FString DisplayName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsPrefix;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Tier;

    // GAS Integration - Effects applied by this affix
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<TSubclassOf<UGameplayEffect>> GameplayEffects;

    // Abilities granted by this affix
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TArray<TSubclassOf<UGameplayAbility>> GrantedAbilities;

    // Tags for identification and interaction
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FGameplayTagContainer AffixTags;
};

UCLASS(BlueprintType, Blueprintable)
class GAMEPROJECT_API UAffixComponent : public UItemComponent
{
    GENERATED_BODY()

public:
    UAffixComponent();

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Affixes")
    TArray<FItemAffix> Prefixes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Affixes")
    TArray<FItemAffix> Suffixes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Limits")
    int32 MaxPrefixes = 3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Limits")
    int32 MaxSuffixes = 3;

    // Affix management
    UFUNCTION(BlueprintCallable, Category = "Affixes")
    bool AddAffix(const FItemAffix& Affix);

    UFUNCTION(BlueprintCallable, Category = "Affixes")
    bool RemoveAffix(const FString& AffixID);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Affixes")
    TArray<FItemAffix> GetAllAffixes() const;

    virtual bool Validate() const override;
    virtual void ApplyToASC(UAbilitySystemComponent* ASC) override;
    virtual void RemoveFromASC(UAbilitySystemComponent* ASC) override;

private:
    // Track applied effects per affix
    UPROPERTY()
    TMap<FString, TArray<FActiveGameplayEffectHandle>> AffixEffectHandles;
    
    UPROPERTY()
    TMap<FString, TArray<FGameplayAbilitySpecHandle>> AffixAbilityHandles;
};

// =============================================================================
// SOCKET COMPONENT
// =============================================================================

UENUM(BlueprintType)
enum class ESocketColor : uint8
{
    Red,
    Green, 
    Blue,
    White
};

USTRUCT(BlueprintType)
struct FItemSocket
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    ESocketColor Color = ESocketColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TObjectPtr<UItemObject> SocketedGem;

    FItemSocket() : SocketedGem(nullptr) {}
};

USTRUCT(BlueprintType)
struct FSocketLink
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Socket1Index = -1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 Socket2Index = -1;
};

UCLASS(BlueprintType, Blueprintable)
class GAMEPROJECT_API USocketComponent : public UItemComponent
{
    GENERATED_BODY()

public:
    USocketComponent();

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sockets")
    TArray<FItemSocket> Sockets;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sockets")
    TArray<FSocketLink> Links;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Limits")
    int32 MaxSockets = 6;

    // Socket management
    UFUNCTION(BlueprintCallable, Category = "Sockets")
    bool AddSocket(ESocketColor Color);

    UFUNCTION(BlueprintCallable, Category = "Sockets")
    bool LinkSockets(int32 Socket1, int32 Socket2);

    UFUNCTION(BlueprintCallable, Category = "Sockets")
    bool SocketGem(int32 SocketIndex, UItemObject* Gem);

    UFUNCTION(BlueprintCallable, Category = "Sockets")
    UItemObject* UnsocketGem(int32 SocketIndex);

    virtual bool Validate() const override;
    virtual void ApplyToASC(UAbilitySystemComponent* ASC) override;
    virtual void RemoveFromASC(UAbilitySystemComponent* ASC) override;

private:
    void ApplySocketedGems(UAbilitySystemComponent* ASC);
    void RemoveSocketedGems(UAbilitySystemComponent* ASC);
};

// =============================================================================
// QUALITY COMPONENT
// =============================================================================

UCLASS(BlueprintType, Blueprintable)
class GAMEPROJECT_API UQualityComponent : public UItemComponent
{
    GENERATED_BODY()

public:
    UQualityComponent();

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality", meta = (ClampMin = "0", ClampMax = "30"))
    int32 Quality = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Special Properties")
    bool bCorrupted = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Special Properties")
    bool bMirrored = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Influence")
    bool bShaperItem = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Influence")
    bool bElderItem = false;

    // Quality effects - applied based on quality level
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality Effects")
    TSubclassOf<UGameplayEffect> QualityEffect;

    // Special property effects
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Special Effects")
    TSubclassOf<UGameplayEffect> CorruptionEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Influence Effects")
    TSubclassOf<UGameplayEffect> ShaperEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Influence Effects")
    TSubclassOf<UGameplayEffect> ElderEffect;

    virtual bool Validate() const override;
    virtual void ApplyToASC(UAbilitySystemComponent* ASC) override;
    virtual void RemoveFromASC(UAbilitySystemComponent* ASC) override;

private:
    UPROPERTY()
    TArray<FActiveGameplayEffectHandle> QualityEffectHandles;
};

// =============================================================================
// ITEM FACTORY & UTILITIES
// =============================================================================

UCLASS(BlueprintType)
class GAMEPROJECT_API UItemFactory : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    UFUNCTION(BlueprintCallable, Category = "Item Factory", CallInEditor = true)
    static UItemObject* CreateItem();

    UFUNCTION(BlueprintCallable, Category = "Item Factory")
    static UBasicInfoComponent* CreateBasicInfo(const FString& Name, int32 Level, EItemRarity Rarity, const FString& BaseType);

    UFUNCTION(BlueprintCallable, Category = "Item Factory")
    static UAffixComponent* CreateAffixComponent(int32 MaxPrefixes = 3, int32 MaxSuffixes = 3);

    UFUNCTION(BlueprintCallable, Category = "Item Factory")
    static USocketComponent* CreateSocketComponent(int32 MaxSockets = 6);

    UFUNCTION(BlueprintCallable, Category = "Item Factory")
    static UQualityComponent* CreateQualityComponent();

    // Utility functions
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Item Utils")
    static FGameplayTag GetComponentTypeTag(const FString& ComponentName);
};

// =============================================================================
// ITEM MANAGER COMPONENT
// =============================================================================

UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class GAMEPROJECT_API UItemManagerComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UItemManagerComponent();

protected:
    UPROPERTY()
    TWeakObjectPtr<UAbilitySystemComponent> OwnerASC;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Items")
    TArray<UItemObject*> EquippedItems;

public:
    virtual void BeginPlay() override;

    // Item management
    UFUNCTION(BlueprintCallable, Category = "Items")
    bool EquipItem(UItemObject* Item);

    UFUNCTION(BlueprintCallable, Category = "Items")
    bool UnequipItem(UItemObject* Item);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Items")
    TArray<UItemObject*> GetEquippedItems() const { return EquippedItems; }

    // GAS Integration
    UFUNCTION(BlueprintCallable, Category = "GAS")
    void RefreshAllItemEffects();

private:
    void CacheAbilitySystemComponent();
};

// =============================================================================
// GAMEPLAY TAG DEFINITIONS
// =============================================================================

// Add to GameplayTags.ini:
// [/Script/GameplayTags.GameplayTagsSettings]
// +GameplayTagList=(Tag="Item.Component.BasicInfo",DevComment="Basic item information component")
// +GameplayTagList=(Tag="Item.Component.Affix",DevComment="Item affix component")
// +GameplayTagList=(Tag="Item.Component.Socket",DevComment="Item socket component") 
// +GameplayTagList=(Tag="Item.Component.Quality",DevComment="Item quality component")
// +GameplayTagList=(Tag="Item.Affix.Prefix",DevComment="Prefix affix tag")
// +GameplayTagList=(Tag="Item.Affix.Suffix",DevComment="Suffix affix tag")
// +GameplayTagList=(Tag="Item.Socket.Red",DevComment="Red socket")
// +GameplayTagList=(Tag="Item.Socket.Green",DevComment="Green socket")
// +GameplayTagList=(Tag="Item.Socket.Blue",DevComment="Blue socket")
// +GameplayTagList=(Tag="Item.Socket.White",DevComment="White socket")

// =============================================================================
// EXAMPLE USAGE
// =============================================================================

/*
// C++ Usage Example:
void AMyPlayerCharacter::CreateExampleWeapon()
{
    // Create item
    UItemObject* Weapon = UItemFactory::CreateItem();
    
    // Add basic info
    UBasicInfoComponent* BasicInfo = UItemFactory::CreateBasicInfo(
        TEXT("Vicious Blade"), 65, EItemRarity::Rare, TEXT("Runic Sword")
    );
    Weapon->AddComponent(BasicInfo);
    
    // Add affixes with GAS effects
    UAffixComponent* Affixes = UItemFactory::CreateAffixComponent();
    
    FItemAffix PhysicalDamage;
    PhysicalDamage.DisplayName = TEXT("+120% Physical Damage");
    PhysicalDamage.bIsPrefix = true;
    PhysicalDamage.GameplayEffects.Add(PhysicalDamageBoostEffect);
    
    Affixes->AddAffix(PhysicalDamage);
    Weapon->AddComponent(Affixes);
    
    // Add sockets
    USocketComponent* Sockets = UItemFactory::CreateSocketComponent(4);
    Sockets->AddSocket(ESocketColor::Red);
    Sockets->AddSocket(ESocketColor::Red);
    Sockets->LinkSockets(0, 1);
    Weapon->AddComponent(Sockets);
    
    // Apply to character's ability system
    UAbilitySystemComponent* ASC = GetAbilitySystemComponent();
    Weapon->ApplyToAbilitySystem(ASC);
    
    // Store in item manager
    UItemManagerComponent* ItemManager = GetComponentByClass<UItemManagerComponent>();
    ItemManager->EquipItem(Weapon);
}

// Blueprint Usage:
// 1. Create Item Object
// 2. Create components via Item Factory nodes
// 3. Add components to item
// 4. Apply to Ability System Component
// 5. Item effects/abilities automatically integrate with GAS
*/
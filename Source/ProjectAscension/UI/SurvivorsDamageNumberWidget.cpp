#include "SurvivorsDamageNumberWidget.h"
#include "Components/TextBlock.h"
#include "Engine/World.h"

USurvivorsDamageNumberWidget::USurvivorsDamageNumberWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    AnimationDuration = 1.5f;
    FloatDistance = 100.0f;
    FadeOutDelay = 0.5f;
    NormalFontSize = 24;
    CriticalFontSize = 32;
}

void USurvivorsDamageNumberWidget::NativeConstruct()
{
    Super::NativeConstruct();
    
    UpdateDisplay();
}

void USurvivorsDamageNumberWidget::SetDamageNumber(float DamageAmount, bool bIsCritical, bool bIsHealing)
{
    Damage = DamageAmount;
    bIsCriticalHit = bIsCritical;
    bIsHealingNumber = bIsHealing;
    
    UpdateDisplay();
}

void USurvivorsDamageNumberWidget::StartAnimation()
{
    // Play float animation
    PlayFloatAnimation();
    
    // Start fade out after delay
    GetWorld()->GetTimerManager().SetTimer(AnimationTimer, [this]()
    {
        PlayFadeOutAnimation();
    }, FadeOutDelay, false);
    
    // Destroy widget after full animation
    GetWorld()->GetTimerManager().SetTimer(DestroyTimer, this, &USurvivorsDamageNumberWidget::OnAnimationComplete, AnimationDuration, false);
}

void USurvivorsDamageNumberWidget::UpdateDisplay()
{
    if (!DamageText)
    {
        return;
    }
    
    // Format damage text
    FString DamageString;
    if (bIsHealingNumber)
    {
        DamageString = FString::Printf(TEXT("+%.0f"), Damage);
    }
    else
    {
        DamageString = FString::Printf(TEXT("%.0f"), Damage);
        if (bIsCriticalHit)
        {
            DamageString += TEXT("!");
        }
    }
    
    DamageText->SetText(FText::FromString(DamageString));
    
    // Set color based on damage type
    FSlateColor TextColor = NormalDamageColor;
    if (bIsHealingNumber)
    {
        TextColor = HealingColor;
    }
    else if (bIsCriticalHit)
    {
        TextColor = CriticalDamageColor;
    }
    
    DamageText->SetColorAndOpacity(TextColor);
    
    // Set font size
    FSlateFontInfo FontInfo = DamageText->GetFont();
    FontInfo.Size = bIsCriticalHit ? CriticalFontSize : NormalFontSize;
    DamageText->SetFont(FontInfo);
}

void USurvivorsDamageNumberWidget::OnAnimationComplete()
{
    // Clean up timers
    GetWorld()->GetTimerManager().ClearTimer(AnimationTimer);
    GetWorld()->GetTimerManager().ClearTimer(DestroyTimer);
    
    // Remove from parent
    RemoveFromParent();
}

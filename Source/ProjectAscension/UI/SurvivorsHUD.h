#pragma once

#include "CoreMinimal.h"
#include "GameFramework/HUD.h"
#include "SurvivorsHUD.generated.h"

class USurvivorsMainWidget;
class USurvivorsLevelUpWidget;
class USurvivorsGameOverWidget;
class ASurvivorsPlayer<PERSON>haracter;

/**
 * ASurvivorsHUD
 * 
 * Main HUD class for the Survivors game that manages all UI widgets.
 * Handles the display of health, experience, level-up selection, and game over screens.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API ASurvivorsHUD : public AHUD
{
    GENERATED_BODY()

public:
    ASurvivorsHUD();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // Widget management
    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void ShowMainUI();

    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void HideMainUI();

    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void ShowLevelUpUI(const TArray<struct FAbilityUpgradeOption>& UpgradeOptions);

    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void HideLevelUpUI();

    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void ShowGameOverUI(bool bVictory = false);

    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void HideGameOverUI();

    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void ShowPauseUI();

    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void HidePauseUI();

    // Event handlers
    UFUNCTION()
    void OnPlayerLevelUp(int32 NewLevel);

    UFUNCTION()
    void OnUpgradeOptionsAvailable(const TArray<FAbilityUpgradeOption>& UpgradeOptions);

    UFUNCTION()
    void OnPlayerDeath();

    UFUNCTION()
    void OnGameStateChanged();

    // Widget classes
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|UI")
    TSubclassOf<USurvivorsMainWidget> MainWidgetClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|UI")
    TSubclassOf<USurvivorsLevelUpWidget> LevelUpWidgetClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|UI")
    TSubclassOf<USurvivorsGameOverWidget> GameOverWidgetClass;

    // Widget instances
    UPROPERTY(BlueprintReadOnly, Category = "Survivors|UI")
    TObjectPtr<USurvivorsMainWidget> MainWidget;

    UPROPERTY(BlueprintReadOnly, Category = "Survivors|UI")
    TObjectPtr<USurvivorsLevelUpWidget> LevelUpWidget;

    UPROPERTY(BlueprintReadOnly, Category = "Survivors|UI")
    TObjectPtr<USurvivorsGameOverWidget> GameOverWidget;

    // UI state
    UPROPERTY(BlueprintReadOnly, Category = "Survivors|UI")
    bool bIsMainUIVisible = false;

    UPROPERTY(BlueprintReadOnly, Category = "Survivors|UI")
    bool bIsLevelUpUIVisible = false;

    UPROPERTY(BlueprintReadOnly, Category = "Survivors|UI")
    bool bIsGameOverUIVisible = false;

private:
    // Cached references
    UPROPERTY()
    TObjectPtr<ASurvivorsPlayerCharacter> PlayerCharacter;

    UPROPERTY()
    TObjectPtr<class UExperienceManager> ExperienceManager;

    // Initialize UI
    void InitializeUI();
    void BindEvents();

    // Get player character
    ASurvivorsPlayerCharacter* GetPlayerCharacter();

    // Get experience manager
    UExperienceManager* GetExperienceManager();
};

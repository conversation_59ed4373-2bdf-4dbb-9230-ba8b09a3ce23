#include "SurvivorsLevelUpWidget.h"
#include "SurvivorsUpgradeOptionWidget.h"
#include "../Systems/ExperienceManager.h"
#include "../Characters/SurvivorsPlayerCharacter.h"
#include "Components/TextBlock.h"
#include "Components/VerticalBox.h"
#include "Components/HorizontalBox.h"
#include "CommonButtonBase.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"

USurvivorsLevelUpWidget::USurvivorsLevelUpWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    bAutoActivate = true;
    FadeInDuration = 0.3f;
    FadeOutDuration = 0.2f;
}

void USurvivorsLevelUpWidget::NativeConstruct()
{
    Super::NativeConstruct();

    // Set title and subtitle text
    if (LevelUpTitle)
    {
        LevelUpTitle->SetText(LevelUpTitleText);
    }

    if (LevelUpSubtitle)
    {
        LevelUpSubtitle->SetText(LevelUpSubtitleText);
    }

    // Play fade in animation
    PlayFadeInAnimation();
}

void USurvivorsLevelUpWidget::NativeDestruct()
{
    ClearUpgradeWidgets();
    Super::NativeDestruct();
}

TOptional<FUIInputConfig> USurvivorsLevelUpWidget::GetDesiredInputConfig() const
{
    // We want UI-only input mode when this widget is active
    FUIInputConfig Config;
    Config.InputMode = ECommonInputMode::Menu;
    Config.MouseCaptureMode = EMouseCaptureMode::NoCapture;
    return Config;
}

void USurvivorsLevelUpWidget::SetUpgradeOptions(const TArray<FAbilityUpgradeOption>& InUpgradeOptions)
{
    UpgradeOptions = InUpgradeOptions;
    CreateUpgradeOptionWidgets();
}

void USurvivorsLevelUpWidget::CreateUpgradeOptionWidgets()
{
    if (!UpgradeOptionsContainer || !UpgradeOptionWidgetClass)
    {
        return;
    }

    // Clear existing widgets
    ClearUpgradeWidgets();

    // Create new upgrade option widgets
    for (int32 i = 0; i < UpgradeOptions.Num(); i++)
    {
        const FAbilityUpgradeOption& UpgradeOption = UpgradeOptions[i];

        USurvivorsUpgradeOptionWidget* OptionWidget = CreateWidget<USurvivorsUpgradeOptionWidget>(this, UpgradeOptionWidgetClass);
        if (OptionWidget)
        {
            // Setup the upgrade option widget
            OptionWidget->SetUpgradeOption(UpgradeOption, i);
            
            // Bind selection event
            OptionWidget->OnUpgradeSelected.AddDynamic(this, &USurvivorsLevelUpWidget::OnUpgradeSelected);

            // Add to container
            UpgradeOptionsContainer->AddChild(OptionWidget);
            UpgradeOptionWidgets.Add(OptionWidget);

            // Set focus to first option
            if (i == 0)
            {
                OptionWidget->SetFocus();
            }
        }
    }
}

void USurvivorsLevelUpWidget::ClearUpgradeWidgets()
{
    if (UpgradeOptionsContainer)
    {
        UpgradeOptionsContainer->ClearChildren();
    }

    UpgradeOptionWidgets.Empty();
}

void USurvivorsLevelUpWidget::SelectUpgrade(int32 UpgradeIndex)
{
    if (!UpgradeOptions.IsValidIndex(UpgradeIndex))
    {
        UE_LOG(LogTemp, Warning, TEXT("Invalid upgrade index: %d"), UpgradeIndex);
        return;
    }

    // Get experience manager and apply the upgrade
    if (UExperienceManager* ExpManager = GetExperienceManager())
    {
        ExpManager->SelectUpgrade(UpgradeIndex);
    }

    // Play fade out animation and close widget
    PlayFadeOutAnimation();
    
    // Close the widget after a short delay
    FTimerHandle CloseTimer;
    GetWorld()->GetTimerManager().SetTimer(CloseTimer, [this]()
    {
        DeactivateWidget();
    }, FadeOutDuration, false);
}

void USurvivorsLevelUpWidget::OnUpgradeSelected(int32 SelectedIndex)
{
    SelectUpgrade(SelectedIndex);
}

UExperienceManager* USurvivorsLevelUpWidget::GetExperienceManager() const
{
    if (APawn* PlayerPawn = UGameplayStatics::GetPlayerPawn(GetWorld(), 0))
    {
        if (ASurvivorsPlayerCharacter* PlayerCharacter = Cast<ASurvivorsPlayerCharacter>(PlayerPawn))
        {
            return PlayerCharacter->FindComponentByClass<UExperienceManager>();
        }
    }
    return nullptr;
}

#include "SurvivorsHUD.h"
#include "SurvivorsMainWidget.h"
#include "SurvivorsLevelUpWidget.h"
#include "SurvivorsGameOverWidget.h"
#include "../Characters/SurvivorsPlayerCharacter.h"
#include "../Systems/ExperienceManager.h"
#include "Blueprint/UserWidget.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"

ASurvivorsHUD::ASurvivorsHUD()
{
    PrimaryActorTick.bCanEverTick = true;
}

void ASurvivorsHUD::BeginPlay()
{
    Super::BeginPlay();

    // Initialize UI after a short delay to ensure everything is set up
    FTimerHandle InitTimer;
    GetWorldTimerManager().SetTimer(InitTimer, this, &ASurvivorsHUD::InitializeUI, 0.1f, false);
}

void ASurvivorsHUD::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Update UI elements that need frequent updates
    if (MainWidget && bIsMainUIVisible)
    {
        MainWidget->UpdateUI(DeltaTime);
    }
}

void ASurvivorsHUD::InitializeUI()
{
    // Get player character and experience manager
    PlayerCharacter = GetPlayerCharacter();
    ExperienceManager = GetExperienceManager();

    // Create main UI widget
    if (MainWidgetClass)
    {
        MainWidget = CreateWidget<USurvivorsMainWidget>(GetWorld(), MainWidgetClass);
        if (MainWidget)
        {
            MainWidget->AddToViewport(0);
            MainWidget->Initialize(PlayerCharacter);
            bIsMainUIVisible = true;
        }
    }

    // Bind events
    BindEvents();
}

void ASurvivorsHUD::BindEvents()
{
    if (ExperienceManager)
    {
        ExperienceManager->OnLevelUp.AddDynamic(this, &ASurvivorsHUD::OnPlayerLevelUp);
        ExperienceManager->OnUpgradeOptionsAvailable.AddDynamic(this, &ASurvivorsHUD::OnUpgradeOptionsAvailable);
    }

    if (PlayerCharacter)
    {
        // Bind to player death event
        // This would typically be done through a delegate on the character
        // For now, we'll check health in the main widget
    }
}

void ASurvivorsHUD::ShowMainUI()
{
    if (MainWidget && !bIsMainUIVisible)
    {
        MainWidget->SetVisibility(ESlateVisibility::Visible);
        bIsMainUIVisible = true;
    }
}

void ASurvivorsHUD::HideMainUI()
{
    if (MainWidget && bIsMainUIVisible)
    {
        MainWidget->SetVisibility(ESlateVisibility::Hidden);
        bIsMainUIVisible = false;
    }
}

void ASurvivorsHUD::ShowLevelUpUI(const TArray<FAbilityUpgradeOption>& UpgradeOptions)
{
    if (bIsLevelUpUIVisible)
    {
        return;
    }

    // Create level up widget if it doesn't exist
    if (!LevelUpWidget && LevelUpWidgetClass)
    {
        LevelUpWidget = CreateWidget<USurvivorsLevelUpWidget>(GetWorld(), LevelUpWidgetClass);
    }

    if (LevelUpWidget)
    {
        LevelUpWidget->AddToViewport(10); // Higher Z-order than main UI
        LevelUpWidget->SetUpgradeOptions(UpgradeOptions);
        LevelUpWidget->SetVisibility(ESlateVisibility::Visible);
        bIsLevelUpUIVisible = true;

        // Set input mode to UI only
        if (APlayerController* PC = GetOwningPlayerController())
        {
            FInputModeUIOnly InputMode;
            InputMode.SetWidgetToFocus(LevelUpWidget->TakeWidget());
            PC->SetInputMode(InputMode);
            PC->SetShowMouseCursor(true);
        }
    }
}

void ASurvivorsHUD::HideLevelUpUI()
{
    if (LevelUpWidget && bIsLevelUpUIVisible)
    {
        LevelUpWidget->RemoveFromParent();
        bIsLevelUpUIVisible = false;

        // Restore input mode to game only
        if (APlayerController* PC = GetOwningPlayerController())
        {
            FInputModeGameOnly InputMode;
            PC->SetInputMode(InputMode);
            PC->SetShowMouseCursor(false);
        }
    }
}

void ASurvivorsHUD::ShowGameOverUI(bool bVictory)
{
    if (bIsGameOverUIVisible)
    {
        return;
    }

    // Create game over widget if it doesn't exist
    if (!GameOverWidget && GameOverWidgetClass)
    {
        GameOverWidget = CreateWidget<USurvivorsGameOverWidget>(GetWorld(), GameOverWidgetClass);
    }

    if (GameOverWidget)
    {
        GameOverWidget->AddToViewport(20); // Highest Z-order
        GameOverWidget->SetGameOverState(bVictory);
        GameOverWidget->SetVisibility(ESlateVisibility::Visible);
        bIsGameOverUIVisible = true;

        // Set input mode to UI only
        if (APlayerController* PC = GetOwningPlayerController())
        {
            FInputModeUIOnly InputMode;
            InputMode.SetWidgetToFocus(GameOverWidget->TakeWidget());
            PC->SetInputMode(InputMode);
            PC->SetShowMouseCursor(true);
        }
    }
}

void ASurvivorsHUD::HideGameOverUI()
{
    if (GameOverWidget && bIsGameOverUIVisible)
    {
        GameOverWidget->RemoveFromParent();
        bIsGameOverUIVisible = false;

        // Restore input mode
        if (APlayerController* PC = GetOwningPlayerController())
        {
            FInputModeGameOnly InputMode;
            PC->SetInputMode(InputMode);
            PC->SetShowMouseCursor(false);
        }
    }
}

void ASurvivorsHUD::ShowPauseUI()
{
    // TODO: Implement pause UI
    UGameplayStatics::SetGamePaused(GetWorld(), true);
}

void ASurvivorsHUD::HidePauseUI()
{
    // TODO: Implement pause UI
    UGameplayStatics::SetGamePaused(GetWorld(), false);
}

// Event handlers
void ASurvivorsHUD::OnPlayerLevelUp(int32 NewLevel)
{
    UE_LOG(LogTemp, Log, TEXT("HUD: Player leveled up to %d"), NewLevel);
    
    // The level up UI will be shown when upgrade options are available
}

void ASurvivorsHUD::OnUpgradeOptionsAvailable(const TArray<FAbilityUpgradeOption>& UpgradeOptions)
{
    ShowLevelUpUI(UpgradeOptions);
}

void ASurvivorsHUD::OnPlayerDeath()
{
    ShowGameOverUI(false);
}

void ASurvivorsHUD::OnGameStateChanged()
{
    // Handle game state changes
}

ASurvivorsPlayerCharacter* ASurvivorsHUD::GetPlayerCharacter()
{
    if (!PlayerCharacter)
    {
        if (APawn* Pawn = UGameplayStatics::GetPlayerPawn(GetWorld(), 0))
        {
            PlayerCharacter = Cast<ASurvivorsPlayerCharacter>(Pawn);
        }
    }
    return PlayerCharacter;
}

UExperienceManager* ASurvivorsHUD::GetExperienceManager()
{
    if (!ExperienceManager && PlayerCharacter)
    {
        ExperienceManager = PlayerCharacter->FindComponentByClass<UExperienceManager>();
    }
    return ExperienceManager;
}

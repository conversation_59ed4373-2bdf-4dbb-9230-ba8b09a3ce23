#pragma once

#include "CoreMinimal.h"
#include "CommonUserWidget.h"
#include "SurvivorsMainWidget.generated.h"

class UProgressBar;
class UTextBlock;
class UImage;
class UCanvasPanel;
class UH<PERSON>zontalBox;
class UVerticalBox;
class ASurvivorsPlayer<PERSON>haracter;
class USurvivorsAbilitySystemComponent;

/**
 * USurvivorsMainWidget
 * 
 * Main gameplay UI widget that displays health, experience, level, and other game information.
 * Uses CommonUI for consistent styling and input handling.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API USurvivorsMainWidget : public UCommonUserWidget
{
    GENERATED_BODY()

public:
    USurvivorsMainWidget(const FObjectInitializer& ObjectInitializer);

    // Initialization
    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void Initialize(ASurvivorsPlayerCharacter* InPlayerCharacter);

    // Update functions
    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void UpdateUI(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void UpdateHealthBar();

    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void UpdateExperienceBar();

    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void UpdateLevelDisplay();

    UFUNCTION(BlueprintCallable, Category = "Survivors|UI")
    void UpdateGameStats();

    // Event handlers (simplified to avoid FOnAttributeChangeData in header)
    UFUNCTION()
    void OnHealthChanged();

    UFUNCTION()
    void OnExperienceChanged();

    UFUNCTION()
    void OnLevelChanged();

protected:
    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;

    // Widget components - bind these in Blueprint
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UProgressBar> HealthBar;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UProgressBar> ExperienceBar;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UTextBlock> HealthText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UTextBlock> LevelText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UTextBlock> ExperienceText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UTextBlock> WaveText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UTextBlock> TimeText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UTextBlock> EnemyCountText;

    // Optional minimap
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UImage> MinimapImage;

    // Container panels
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UCanvasPanel> MainCanvas;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UHorizontalBox> TopBar;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|UI")
    TObjectPtr<UVerticalBox> StatsPanel;

    // UI settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|UI")
    float UpdateInterval = 0.1f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|UI")
    bool bShowDetailedStats = true;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|UI")
    bool bShowMinimap = false;

    // Colors for health bar
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|UI")
    FLinearColor HealthBarColorHigh = FLinearColor::Green;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|UI")
    FLinearColor HealthBarColorMedium = FLinearColor::Yellow;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|UI")
    FLinearColor HealthBarColorLow = FLinearColor::Red;

    // Colors for experience bar
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|UI")
    FLinearColor ExperienceBarColor = FLinearColor::Blue;

private:
    // Cached references
    UPROPERTY()
    TObjectPtr<ASurvivorsPlayerCharacter> PlayerCharacter;

    UPROPERTY()
    TObjectPtr<USurvivorsAbilitySystemComponent> AbilitySystemComponent;

    // Update timer
    float UpdateTimer = 0.0f;

    // Game start time for elapsed time calculation
    float GameStartTime = 0.0f;

    // Bind to attribute changes
    void BindAttributeChanges();

    // Unbind from attribute changes
    void UnbindAttributeChanges();

    // Get health bar color based on health percentage
    FLinearColor GetHealthBarColor(float HealthPercent) const;

    // Format time as MM:SS
    FText FormatTime(float TimeInSeconds) const;

    // Get current wave number
    int32 GetCurrentWave() const;

    // Get enemy count
    int32 GetEnemyCount() const;
};

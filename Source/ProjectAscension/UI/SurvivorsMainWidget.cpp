#include "SurvivorsMainWidget.h"
#include "../Characters/SurvivorsPlayerCharacter.h"
#include "../GAS/SurvivorsAbilitySystemComponent.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "../ProjectAscensionGameMode.h"
#include "Components/ProgressBar.h"
#include "Components/TextBlock.h"
#include "Components/Image.h"
#include "Components/CanvasPanel.h"
#include "Components/HorizontalBox.h"
#include "Components/VerticalBox.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "AttributeSet.h"

USurvivorsMainWidget::USurvivorsMainWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    UpdateInterval = 0.1f;
    bShowDetailedStats = true;
    bShowMinimap = false;
}

void USurvivorsMainWidget::NativeConstruct()
{
    Super::NativeConstruct();

    // Record game start time
    GameStartTime = GetWorld()->GetTimeSeconds();

    // Initialize update timer
    UpdateTimer = 0.0f;
}

void USurvivorsMainWidget::NativeDestruct()
{
    UnbindAttributeChanges();
    Super::NativeDestruct();
}

void USurvivorsMainWidget::Initialize(ASurvivorsPlayerCharacter* InPlayerCharacter)
{
    PlayerCharacter = InPlayerCharacter;
    
    if (PlayerCharacter)
    {
        AbilitySystemComponent = Cast<USurvivorsAbilitySystemComponent>(PlayerCharacter->GetAbilitySystemComponent());
        BindAttributeChanges();
        
        // Initial UI update
        UpdateHealthBar();
        UpdateExperienceBar();
        UpdateLevelDisplay();
        UpdateGameStats();
    }
}

void USurvivorsMainWidget::UpdateUI(float DeltaTime)
{
    UpdateTimer += DeltaTime;
    
    if (UpdateTimer >= UpdateInterval)
    {
        UpdateGameStats();
        UpdateTimer = 0.0f;
    }
}

void USurvivorsMainWidget::UpdateHealthBar()
{
    if (!PlayerCharacter || !HealthBar)
    {
        return;
    }

    const float HealthPercent = PlayerCharacter->GetHealthPercent();
    HealthBar->SetPercent(HealthPercent);
    
    // Update health bar color based on health percentage
    HealthBar->SetFillColorAndOpacity(GetHealthBarColor(HealthPercent));

    // Update health text
    if (HealthText)
    {
        const float CurrentHealth = PlayerCharacter->GetHealth();
        const float MaxHealth = PlayerCharacter->GetMaxHealth();
        const FText HealthString = FText::FromString(FString::Printf(TEXT("%.0f / %.0f"), CurrentHealth, MaxHealth));
        HealthText->SetText(HealthString);
    }
}

void USurvivorsMainWidget::UpdateExperienceBar()
{
    if (!PlayerCharacter || !ExperienceBar)
    {
        return;
    }

    const float ExperiencePercent = PlayerCharacter->GetExperiencePercent();
    ExperienceBar->SetPercent(ExperiencePercent);
    ExperienceBar->SetFillColorAndOpacity(ExperienceBarColor);

    // Update experience text
    if (ExperienceText)
    {
        const float CurrentExp = PlayerCharacter->GetExperience();
        const float ExpToNext = AbilitySystemComponent ? AbilitySystemComponent->GetExperienceToNextLevel() : 100.0f;
        const FText ExpString = FText::FromString(FString::Printf(TEXT("%.0f / %.0f"), CurrentExp, ExpToNext));
        ExperienceText->SetText(ExpString);
    }
}

void USurvivorsMainWidget::UpdateLevelDisplay()
{
    if (!PlayerCharacter || !LevelText)
    {
        return;
    }

    const int32 CurrentLevel = PlayerCharacter->GetCharacterLevel();
    const FText LevelString = FText::FromString(FString::Printf(TEXT("Level %d"), CurrentLevel));
    LevelText->SetText(LevelString);
}

void USurvivorsMainWidget::UpdateGameStats()
{
    // Update wave text
    if (WaveText)
    {
        const int32 CurrentWave = GetCurrentWave();
        const FText WaveString = FText::FromString(FString::Printf(TEXT("Wave %d"), CurrentWave));
        WaveText->SetText(WaveString);
    }

    // Update time text
    if (TimeText)
    {
        const float ElapsedTime = GetWorld()->GetTimeSeconds() - GameStartTime;
        TimeText->SetText(FormatTime(ElapsedTime));
    }

    // Update enemy count text
    if (EnemyCountText && bShowDetailedStats)
    {
        const int32 EnemyCount = GetEnemyCount();
        const FText EnemyString = FText::FromString(FString::Printf(TEXT("Enemies: %d"), EnemyCount));
        EnemyCountText->SetText(EnemyString);
    }
}

void USurvivorsMainWidget::BindAttributeChanges()
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    const USurvivorsAttributeSet* AttributeSet = AbilitySystemComponent->GetSurvivorsAttributeSet();
    if (!AttributeSet)
    {
        return;
    }

    // Bind to health changes using lambda to avoid FOnAttributeChangeData in header
    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetHealthAttribute())
        .AddLambda([this](const FOnAttributeChangeData& Data) { OnHealthChanged(); });

    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetMaxHealthAttribute())
        .AddLambda([this](const FOnAttributeChangeData& Data) { OnHealthChanged(); });

    // Bind to experience changes
    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetExperienceAttribute())
        .AddLambda([this](const FOnAttributeChangeData& Data) { OnExperienceChanged(); });

    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetExperienceToNextLevelAttribute())
        .AddLambda([this](const FOnAttributeChangeData& Data) { OnExperienceChanged(); });

    // Bind to level changes
    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetLevelAttribute())
        .AddLambda([this](const FOnAttributeChangeData& Data) { OnLevelChanged(); });
}

void USurvivorsMainWidget::UnbindAttributeChanges()
{
    if (!AbilitySystemComponent)
    {
        return;
    }

    const USurvivorsAttributeSet* AttributeSet = AbilitySystemComponent->GetSurvivorsAttributeSet();
    if (!AttributeSet)
    {
        return;
    }

    // Unbind all delegates
    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetHealthAttribute()).RemoveAll(this);
    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetMaxHealthAttribute()).RemoveAll(this);
    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetExperienceAttribute()).RemoveAll(this);
    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetExperienceToNextLevelAttribute()).RemoveAll(this);
    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetLevelAttribute()).RemoveAll(this);
}

// Event handlers (simplified without FOnAttributeChangeData parameter)
void USurvivorsMainWidget::OnHealthChanged()
{
    UpdateHealthBar();
}

void USurvivorsMainWidget::OnExperienceChanged()
{
    UpdateExperienceBar();
}

void USurvivorsMainWidget::OnLevelChanged()
{
    UpdateLevelDisplay();
}

// Helper functions
FLinearColor USurvivorsMainWidget::GetHealthBarColor(float HealthPercent) const
{
    if (HealthPercent > 0.6f)
    {
        return HealthBarColorHigh;
    }
    else if (HealthPercent > 0.3f)
    {
        return HealthBarColorMedium;
    }
    else
    {
        return HealthBarColorLow;
    }
}

FText USurvivorsMainWidget::FormatTime(float TimeInSeconds) const
{
    const int32 Minutes = FMath::FloorToInt(TimeInSeconds / 60.0f);
    const int32 Seconds = FMath::FloorToInt(TimeInSeconds) % 60;
    const FString TimeString = FString::Printf(TEXT("%02d:%02d"), Minutes, Seconds);
    return FText::FromString(TimeString);
}

int32 USurvivorsMainWidget::GetCurrentWave() const
{
    if (AProjectAscensionGameMode* GameMode = Cast<AProjectAscensionGameMode>(UGameplayStatics::GetGameMode(GetWorld())))
    {
        return GameMode->GetCurrentWave();
    }
    return 1;
}

int32 USurvivorsMainWidget::GetEnemyCount() const
{
    // Count all enemy pawns in the world
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), APawn::StaticClass(), FoundActors);
    
    int32 EnemyCount = 0;
    for (AActor* Actor : FoundActors)
    {
        if (Actor && !Actor->IsA<ASurvivorsPlayerCharacter>())
        {
            EnemyCount++;
        }
    }
    
    return EnemyCount;
}

#include "SurvivorsGameOverWidget.h"
#include "../Characters/SurvivorsPlayerCharacter.h"
#include "../ProjectAscensionGameMode.h"
#include "Components/TextBlock.h"
#include "Components/VerticalBox.h"
#include "Components/HorizontalBox.h"
#include "CommonButtonBase.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "Kismet/KismetSystemLibrary.h"

USurvivorsGameOverWidget::USurvivorsGameOverWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    bAutoActivate = true;
    FadeInDuration = 0.5f;
    StatsDisplayDelay = 1.0f;
}

void USurvivorsGameOverWidget::NativeConstruct()
{
    Super::NativeConstruct();

    // Bind button events
    if (RestartButton)
    {
        RestartButton->OnClicked().AddUObject(this, &USurvivorsGameOverWidget::OnRestartClicked);
    }

    if (MainMenuButton)
    {
        MainMenuButton->OnClicked().AddUObject(this, &USurvivorsGameOverWidget::OnMainMenuClicked);
    }

    if (QuitButton)
    {
        QuitButton->OnClicked().AddUObject(this, &USurvivorsGameOverWidget::OnQuitClicked);
    }

    // Collect and display stats
    CollectGameStats();
    DisplayFinalStats();

    // Play fade in animation
    PlayFadeInAnimation();

    // Play stats animation after delay
    FTimerHandle StatsTimer;
    GetWorld()->GetTimerManager().SetTimer(StatsTimer, this, &USurvivorsGameOverWidget::PlayStatsAnimation, StatsDisplayDelay, false);

    // Set focus to restart button
    if (RestartButton)
    {
        RestartButton->SetFocus();
    }
}

TOptional<FUIInputConfig> USurvivorsGameOverWidget::GetDesiredInputConfig() const
{
    // We want UI-only input mode when this widget is active
    FUIInputConfig Config;
    Config.InputMode = ECommonInputMode::Menu;
    Config.MouseCaptureMode = EMouseCaptureMode::NoCapture;
    return Config;
}

void USurvivorsGameOverWidget::SetGameOverState(bool bVictory)
{
    bIsVictory = bVictory;

    // Update title and subtitle
    if (GameOverTitle)
    {
        GameOverTitle->SetText(bIsVictory ? VictoryTitleText : DefeatTitleText);
        GameOverTitle->SetColorAndOpacity(bIsVictory ? VictoryTitleColor : DefeatTitleColor);
    }

    if (GameOverSubtitle)
    {
        GameOverSubtitle->SetText(bIsVictory ? VictorySubtitleText : DefeatSubtitleText);
    }
}

void USurvivorsGameOverWidget::DisplayFinalStats()
{
    // Display final level
    if (FinalLevelText)
    {
        const FText LevelText = FText::FromString(FString::Printf(TEXT("Final Level: %d"), GameStats.FinalLevel));
        FinalLevelText->SetText(LevelText);
    }

    // Display survival time
    if (SurvivalTimeText)
    {
        const FText TimeText = FText::FromString(FString::Printf(TEXT("Survival Time: %s"), *FormatTime(GameStats.SurvivalTime).ToString()));
        SurvivalTimeText->SetText(TimeText);
    }

    // Display enemies killed
    if (EnemiesKilledText)
    {
        const FText KillsText = FText::FromString(FString::Printf(TEXT("Enemies Killed: %d"), GameStats.EnemiesKilled));
        EnemiesKilledText->SetText(KillsText);
    }

    // Display waves completed
    if (WavesCompletedText)
    {
        const FText WavesText = FText::FromString(FString::Printf(TEXT("Waves Completed: %d"), GameStats.WavesCompleted));
        WavesCompletedText->SetText(WavesText);
    }

    // Display damage dealt
    if (DamageDealtText)
    {
        const FText DamageText = FText::FromString(FString::Printf(TEXT("Damage Dealt: %.0f"), GameStats.DamageDealt));
        DamageDealtText->SetText(DamageText);
    }
}

void USurvivorsGameOverWidget::CollectGameStats()
{
    // Get player character for stats
    if (ASurvivorsPlayerCharacter* PlayerCharacter = GetPlayerCharacter())
    {
        GameStats.FinalLevel = PlayerCharacter->GetCharacterLevel();
    }

    // Get game mode for additional stats
    if (AProjectAscensionGameMode* GameMode = GetGameMode())
    {
        // TODO: Add methods to game mode to track these stats
        GameStats.WavesCompleted = 1; // Placeholder
        GameStats.EnemiesKilled = 0; // Placeholder
        GameStats.DamageDealt = 0.0f; // Placeholder
    }

    // Calculate survival time (placeholder - should be tracked by game mode)
    GameStats.SurvivalTime = GetWorld()->GetTimeSeconds() - GameStartTime;
}

void USurvivorsGameOverWidget::OnRestartClicked()
{
    UE_LOG(LogTemp, Log, TEXT("Restart button clicked"));

    // Restart the current level
    UGameplayStatics::OpenLevel(GetWorld(), FName(*UGameplayStatics::GetCurrentLevelName(GetWorld())));
}

void USurvivorsGameOverWidget::OnMainMenuClicked()
{
    UE_LOG(LogTemp, Log, TEXT("Main menu button clicked"));

    // TODO: Load main menu level
    // For now, just restart the game
    UGameplayStatics::OpenLevel(GetWorld(), FName(*UGameplayStatics::GetCurrentLevelName(GetWorld())));
}

void USurvivorsGameOverWidget::OnQuitClicked()
{
    UE_LOG(LogTemp, Log, TEXT("Quit button clicked"));

    // Quit the game
    UKismetSystemLibrary::QuitGame(GetWorld(), nullptr, EQuitPreference::Quit, false);
}

FText USurvivorsGameOverWidget::FormatTime(float TimeInSeconds) const
{
    const int32 Minutes = FMath::FloorToInt(TimeInSeconds / 60.0f);
    const int32 Seconds = FMath::FloorToInt(TimeInSeconds) % 60;
    const FString TimeString = FString::Printf(TEXT("%02d:%02d"), Minutes, Seconds);
    return FText::FromString(TimeString);
}

ASurvivorsPlayerCharacter* USurvivorsGameOverWidget::GetPlayerCharacter() const
{
    if (APawn* PlayerPawn = UGameplayStatics::GetPlayerPawn(GetWorld(), 0))
    {
        return Cast<ASurvivorsPlayerCharacter>(PlayerPawn);
    }
    return nullptr;
}

AProjectAscensionGameMode* USurvivorsGameOverWidget::GetGameMode() const
{
    return Cast<AProjectAscensionGameMode>(UGameplayStatics::GetGameMode(GetWorld()));
}

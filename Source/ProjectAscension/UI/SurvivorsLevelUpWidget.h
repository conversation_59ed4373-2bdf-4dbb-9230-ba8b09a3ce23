#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "../Systems/ExperienceManager.h"
#include "SurvivorsLevelUpWidget.generated.h"

class UCommonButtonBase;
class UTextBlock;
class UImage;
class UVertical<PERSON>ox;
class UHorizontalBox;
class USurvivorsUpgradeOptionWidget;

/**
 * USurvivorsLevelUpWidget
 * 
 * Level-up selection screen that displays available upgrade options.
 * Uses CommonUI for consistent styling and controller/keyboard navigation.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API USurvivorsLevelUpWidget : public UCommonActivatableWidget
{
    GENERATED_BODY()

public:
    USurvivorsLevelUpWidget(const FObjectInitializer& ObjectInitializer);

    // Setup upgrade options
    UFUNCTION(BlueprintCallable, Category = "Survivors|LevelUp")
    void SetUpgradeOptions(const TArray<FAbilityUpgradeOption>& InUpgradeOptions);

    // Handle upgrade selection
    UFUNCTION(BlueprintCallable, Category = "Survivors|LevelUp")
    void SelectUpgrade(int32 UpgradeIndex);

protected:
    virtual void NativeConstruct() override;
    virtual void NativeDestruct() override;

    // CommonActivatableWidget interface
    virtual TOptional<FUIInputConfig> GetDesiredInputConfig() const override;

    // Create upgrade option widgets
    UFUNCTION(BlueprintCallable, Category = "Survivors|LevelUp")
    void CreateUpgradeOptionWidgets();

    // Clear existing upgrade widgets
    UFUNCTION(BlueprintCallable, Category = "Survivors|LevelUp")
    void ClearUpgradeWidgets();

    // Handle input
    UFUNCTION()
    void OnUpgradeSelected(int32 SelectedIndex);

    // Widget components - bind these in Blueprint
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|LevelUp")
    TObjectPtr<UTextBlock> LevelUpTitle;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|LevelUp")
    TObjectPtr<UTextBlock> LevelUpSubtitle;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|LevelUp")
    TObjectPtr<UVerticalBox> UpgradeOptionsContainer;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|LevelUp")
    TObjectPtr<UHorizontalBox> ButtonContainer;

    // Upgrade option widget class
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|LevelUp")
    TSubclassOf<USurvivorsUpgradeOptionWidget> UpgradeOptionWidgetClass;

    // UI settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|LevelUp")
    FText LevelUpTitleText = FText::FromString("LEVEL UP!");

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|LevelUp")
    FText LevelUpSubtitleText = FText::FromString("Choose an upgrade:");

    // Animation settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|LevelUp")
    float FadeInDuration = 0.3f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|LevelUp")
    float FadeOutDuration = 0.2f;

    // Animation functions (implemented in Blueprint)
    UFUNCTION(BlueprintImplementableEvent, Category = "Survivors|LevelUp")
    void PlayFadeInAnimation();

    UFUNCTION(BlueprintImplementableEvent, Category = "Survivors|LevelUp")
    void PlayFadeOutAnimation();

private:
    // Current upgrade options
    TArray<FAbilityUpgradeOption> UpgradeOptions;

    // Created upgrade option widgets
    UPROPERTY()
    TArray<TObjectPtr<USurvivorsUpgradeOptionWidget>> UpgradeOptionWidgets;

    // Get experience manager
    class UExperienceManager* GetExperienceManager() const;
};

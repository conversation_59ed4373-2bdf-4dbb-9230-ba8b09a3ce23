#include "SurvivorsUpgradeOptionWidget.h"
#include "Components/TextBlock.h"
#include "Components/Image.h"
#include "Components/Border.h"
#include "Components/HorizontalBox.h"
#include "Components/VerticalBox.h"
#include "Engine/Texture2D.h"

USurvivorsUpgradeOptionWidget::USurvivorsUpgradeOptionWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Set default style
    bIsFocusable = true;
    
    // Set default colors
    NormalBorderColor = FLinearColor(0.1f, 0.1f, 0.1f, 0.8f);
    HoveredBorderColor = FLinearColor(0.3f, 0.3f, 0.8f, 1.0f);
    SelectedBorderColor = FLinearColor(0.8f, 0.8f, 0.2f, 1.0f);
    
    NormalTextColor = FSlateColor(FLinearColor::White);
    HoveredTextColor = FSlateColor(FLinearColor::Yellow);
    
    HoverAnimationDuration = 0.1f;
    ClickAnimationDuration = 0.05f;
}

void USurvivorsUpgradeOptionWidget::NativeConstruct()
{
    Super::NativeConstruct();
    
    // Initialize visual state
    UpdateVisualState();
}

void USurvivorsUpgradeOptionWidget::SetUpgradeOption(const FAbilityUpgradeOption& InUpgradeOption, int32 InUpgradeIndex)
{
    UpgradeOption = InUpgradeOption;
    UpgradeIndex = InUpgradeIndex;
    
    UpdateDisplay();
}

void USurvivorsUpgradeOptionWidget::UpdateDisplay()
{
    // Update upgrade name
    if (UpgradeName)
    {
        UpgradeName->SetText(UpgradeOption.DisplayName);
    }
    
    // Update upgrade description
    if (UpgradeDescription)
    {
        UpgradeDescription->SetText(UpgradeOption.Description);
    }
    
    // Update upgrade icon
    if (UpgradeIcon && UpgradeOption.Icon)
    {
        UpgradeIcon->SetBrushFromTexture(UpgradeOption.Icon);
        UpgradeIcon->SetVisibility(ESlateVisibility::Visible);
    }
    else if (UpgradeIcon)
    {
        UpgradeIcon->SetVisibility(ESlateVisibility::Collapsed);
    }
    
    // Update visual state
    UpdateVisualState();
}

void USurvivorsUpgradeOptionWidget::NativeOnClicked()
{
    Super::NativeOnClicked();
    
    // Play click animation
    PlayClickAnimation();
    
    // Broadcast selection
    OnUpgradeSelected.Broadcast(UpgradeIndex);
    
    UE_LOG(LogTemp, Log, TEXT("Upgrade selected: %s (Index: %d)"), 
           *UpgradeOption.DisplayName.ToString(), UpgradeIndex);
}

void USurvivorsUpgradeOptionWidget::NativeOnHovered()
{
    Super::NativeOnHovered();
    
    bIsHovered = true;
    UpdateVisualState();
    PlayHoverAnimation();
}

void USurvivorsUpgradeOptionWidget::NativeOnUnhovered()
{
    Super::NativeOnUnhovered();
    
    bIsHovered = false;
    UpdateVisualState();
    PlayUnhoverAnimation();
}

void USurvivorsUpgradeOptionWidget::UpdateVisualState()
{
    // Update border color
    if (UpgradeBorder)
    {
        FLinearColor BorderColor = bIsHovered ? HoveredBorderColor : NormalBorderColor;
        UpgradeBorder->SetBrushColor(BorderColor);
    }
    
    // Update text colors
    FSlateColor TextColor = bIsHovered ? HoveredTextColor : NormalTextColor;
    
    if (UpgradeName)
    {
        UpgradeName->SetColorAndOpacity(TextColor);
    }
    
    if (UpgradeDescription)
    {
        UpgradeDescription->SetColorAndOpacity(TextColor);
    }
}

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "SurvivorsDamageNumberWidget.generated.h"

class UTextBlock;

/**
 * USurvivorsDamageNumberWidget
 * 
 * Floating damage number widget that appears when damage is dealt.
 * Provides visual feedback for combat with animated text.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API USurvivorsDamageNumberWidget : public UUserWidget
{
    GENERATED_BODY()

public:
    explicit USurvivorsDamageNumberWidget(const FObjectInitializer& ObjectInitializer);

    // Setup the damage number
    UFUNCTION(BlueprintCallable, Category = "Survivors|DamageNumber")
    void SetDamageNumber(float DamageAmount, bool bIsCritical = false, bool bIsHealing = false);

    // Start the animation
    UFUNCTION(BlueprintCallable, Category = "Survivors|DamageNumber")
    void StartAnimation();

protected:
    virtual void NativeConstruct() override;

    // Widget components
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|DamageNumber")
    TObjectPtr<UTextBlock> DamageText;

    // Visual settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumber")
    FSlateColor NormalDamageColor = FSlateColor(FLinearColor::White);

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumber")
    FSlateColor CriticalDamageColor = FSlateColor(FLinearColor::Yellow);

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumber")
    FSlateColor HealingColor = FSlateColor(FLinearColor::Green);

    // Font settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumber")
    int32 NormalFontSize = 24;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumber")
    int32 CriticalFontSize = 32;

    // Animation settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumber")
    float AnimationDuration = 1.5f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumber")
    float FloatDistance = 100.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumber")
    float FadeOutDelay = 0.5f;

    // Animation functions (implemented in Blueprint)
    UFUNCTION(BlueprintImplementableEvent, Category = "Survivors|DamageNumber")
    void PlayFloatAnimation();

    UFUNCTION(BlueprintImplementableEvent, Category = "Survivors|DamageNumber")
    void PlayFadeOutAnimation();

private:
    // Damage properties
    float Damage = 0.0f;
    bool bIsCriticalHit = false;
    bool bIsHealingNumber = false;

    // Animation timer
    FTimerHandle AnimationTimer;
    FTimerHandle DestroyTimer;

    // Update the display
    void UpdateDisplay();

    // Cleanup
    UFUNCTION()
    void OnAnimationComplete();
};

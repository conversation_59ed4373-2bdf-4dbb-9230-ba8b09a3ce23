#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "SurvivorsGameOverWidget.generated.h"

class UCommonButtonBase;
class UTextBlock;
class UImage;
class UVerticalBox;
class UHorizontalBox;

/**
 * USurvivorsGameOverWidget
 * 
 * Game over screen that displays final stats and provides restart/quit options.
 * Uses CommonUI for consistent styling and input handling.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API USurvivorsGameOverWidget : public UCommonActivatableWidget
{
    GENERATED_BODY()

public:
    explicit USurvivorsGameOverWidget(const FObjectInitializer& ObjectInitializer);

    // Set game over state
    UFUNCTION(BlueprintCallable, Category = "Survivors|GameOver")
    void SetGameOverState(bool bVictory);

    // Display final stats
    UFUNCTION(BlueprintCallable, Category = "Survivors|GameOver")
    void DisplayFinalStats();

protected:
    virtual void NativeConstruct() override;

    // CommonActivatableWidget interface
    virtual TOptional<FUIInputConfig> GetDesiredInputConfig() const override;

    // Button handlers
    UFUNCTION()
    void OnRestartClicked();

    UFUNCTION()
    void OnMainMenuClicked();

    UFUNCTION()
    void OnQuitClicked();

    // Widget components - bind these in Blueprint
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UTextBlock> GameOverTitle;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UTextBlock> GameOverSubtitle;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UVerticalBox> StatsContainer;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UTextBlock> FinalLevelText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UTextBlock> SurvivalTimeText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UTextBlock> EnemiesKilledText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UTextBlock> WavesCompletedText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UTextBlock> DamageDealtText;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UHorizontalBox> ButtonContainer;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UCommonButtonBase> RestartButton;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UCommonButtonBase> MainMenuButton;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|GameOver")
    TObjectPtr<UCommonButtonBase> QuitButton;

    // Text settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|GameOver")
    FText VictoryTitleText = FText::FromString("VICTORY!");

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|GameOver")
    FText DefeatTitleText = FText::FromString("GAME OVER");

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|GameOver")
    FText VictorySubtitleText = FText::FromString("You survived the onslaught!");

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|GameOver")
    FText DefeatSubtitleText = FText::FromString("You have fallen...");

    // Colors
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|GameOver")
    FSlateColor VictoryTitleColor = FSlateColor(FLinearColor::Green);

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|GameOver")
    FSlateColor DefeatTitleColor = FSlateColor(FLinearColor::Red);

    // Animation settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|GameOver")
    float FadeInDuration = 0.5f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|GameOver")
    float StatsDisplayDelay = 1.0f;

    // Animation functions (implemented in Blueprint)
    UFUNCTION(BlueprintImplementableEvent, Category = "Survivors|GameOver")
    void PlayFadeInAnimation();

    UFUNCTION(BlueprintImplementableEvent, Category = "Survivors|GameOver")
    void PlayStatsAnimation();

private:
    // Game state
    bool bIsVictory = false;

    // Game start time for survival time calculation
    float GameStartTime = 0.0f;

    // Stats tracking
    struct FGameStats
    {
        int32 FinalLevel = 1;
        float SurvivalTime = 0.0f;
        int32 EnemiesKilled = 0;
        int32 WavesCompleted = 0;
        float DamageDealt = 0.0f;
    };

    FGameStats GameStats;

    // Collect final game stats
    void CollectGameStats();

    // Format time as MM:SS
    FText FormatTime(float TimeInSeconds) const;

    // Get player character
    class ASurvivorsPlayerCharacter* GetPlayerCharacter() const;

    // Get game mode
    class AProjectAscensionGameMode* GetGameMode() const;
};

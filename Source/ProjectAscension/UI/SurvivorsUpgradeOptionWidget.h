#pragma once

#include "CoreMinimal.h"
#include "CommonButtonBase.h"
#include "../Systems/ExperienceManager.h"
#include "SurvivorsUpgradeOptionWidget.generated.h"

class UTextBlock;
class UImage;
class UBorder;
class UHorizon<PERSON>Box;
class UVerticalBox;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnUpgradeSelected, int32, UpgradeIndex);

/**
 * USurvivorsUpgradeOptionWidget
 * 
 * Individual upgrade option button that displays upgrade information and handles selection.
 * Uses CommonUI button for consistent styling and input handling.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API USurvivorsUpgradeOptionWidget : public UCommonButtonBase
{
    GENERATED_BODY()

public:
    USurvivorsUpgradeOptionWidget(const FObjectInitializer& ObjectInitializer);

    // Delegate for when this upgrade is selected
    UPROPERTY(BlueprintAssignable, Category = "Survivors|Upgrade")
    FOnUpgradeSelected OnUpgradeSelected;

    // Setup the upgrade option
    UFUNCTION(BlueprintCallable, Category = "Survivors|Upgrade")
    void SetUpgradeOption(const FAbilityUpgradeOption& InUpgradeOption, int32 InUpgradeIndex);

    // Get the upgrade option
    UFUNCTION(BlueprintCallable, Category = "Survivors|Upgrade")
    const FAbilityUpgradeOption& GetUpgradeOption() const { return UpgradeOption; }

    // Get the upgrade index
    UFUNCTION(BlueprintCallable, Category = "Survivors|Upgrade")
    int32 GetUpgradeIndex() const { return UpgradeIndex; }

protected:
    virtual void NativeConstruct() override;

    // CommonButtonBase interface
    virtual void NativeOnClicked() override;
    virtual void NativeOnHovered() override;
    virtual void NativeOnUnhovered() override;

    // Update the display
    UFUNCTION(BlueprintCallable, Category = "Survivors|Upgrade")
    void UpdateDisplay();

    // Widget components - bind these in Blueprint
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|Upgrade")
    TObjectPtr<UImage> UpgradeIcon;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|Upgrade")
    TObjectPtr<UTextBlock> UpgradeName;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|Upgrade")
    TObjectPtr<UTextBlock> UpgradeDescription;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|Upgrade")
    TObjectPtr<UBorder> UpgradeBorder;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|Upgrade")
    TObjectPtr<UHorizontalBox> ContentContainer;

    UPROPERTY(BlueprintReadOnly, meta = (BindWidget), Category = "Survivors|Upgrade")
    TObjectPtr<UVerticalBox> TextContainer;

    // Visual settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Upgrade")
    FLinearColor NormalBorderColor = FLinearColor(0.1f, 0.1f, 0.1f, 0.8f);

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Upgrade")
    FLinearColor HoveredBorderColor = FLinearColor(0.3f, 0.3f, 0.8f, 1.0f);

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Upgrade")
    FLinearColor SelectedBorderColor = FLinearColor(0.8f, 0.8f, 0.2f, 1.0f);

    // Text styling
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Upgrade")
    FSlateColor NormalTextColor = FSlateColor(FLinearColor::White);

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Upgrade")
    FSlateColor HoveredTextColor = FSlateColor(FLinearColor::Yellow);

    // Animation settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Upgrade")
    float HoverAnimationDuration = 0.1f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Upgrade")
    float ClickAnimationDuration = 0.05f;

    // Animation functions (implemented in Blueprint)
    UFUNCTION(BlueprintImplementableEvent, Category = "Survivors|Upgrade")
    void PlayHoverAnimation();

    UFUNCTION(BlueprintImplementableEvent, Category = "Survivors|Upgrade")
    void PlayUnhoverAnimation();

    UFUNCTION(BlueprintImplementableEvent, Category = "Survivors|Upgrade")
    void PlayClickAnimation();

private:
    // Current upgrade option data
    FAbilityUpgradeOption UpgradeOption;
    int32 UpgradeIndex = -1;

    // Visual state
    bool bIsHovered = false;

    // Update visual state
    void UpdateVisualState();
};

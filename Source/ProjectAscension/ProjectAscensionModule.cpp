#include "ProjectAscension.h"
#include "Modules/ModuleManager.h"
#include "GAS/SurvivorsGameplayTags.h"

class FProjectAscensionModule : public IModuleInterface
{
public:
    virtual void StartupModule() override
    {
        // Initialize native gameplay tags
        FSurvivorsGameplayTags::InitializeNativeTags();
    }

    virtual void ShutdownModule() override
    {
        // Cleanup if needed
    }
};

IMPLEMENT_PRIMARY_GAME_MODULE(FProjectAscensionModule, ProjectAscension, "ProjectAscension");

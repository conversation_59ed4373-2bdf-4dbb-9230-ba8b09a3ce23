#include "ProjectAscensionGameMode.h"
#include "Characters/SurvivorsEnemyCharacter.h"
#include "Characters/SurvivorsPlayerCharacter.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/PlayerController.h"
#include "UObject/ConstructorHelpers.h"

AProjectAscensionGameMode::AProjectAscensionGameMode()
{
    // Enable ticking
    PrimaryActorTick.bCanEverTick = true;

    // Use a simple pawn for now - will be set in Blueprint
    static ConstructorHelpers::FClassFinder<APawn> PlayerPawnBPClass(TEXT("/Game/ThirdPerson/Blueprints/BP_ThirdPersonCharacter"));
    if (PlayerPawnBPClass.Class != NULL)
    {
        DefaultPawnClass = PlayerPawnBPClass.Class;
    }

    // Set default game settings
    SpawnInterval = 2.0f;
    EnemiesPerWave = 5;
    SpawnRadius = 1500.0f;
    MinSpawnDistance = 800.0f;
    DifficultyIncreaseInterval = 30.0f;
    DifficultyMultiplier = 1.1f;
}

void AProjectAscensionGameMode::BeginPlay()
{
    Super::BeginPlay();

    // Initialize spawning
    SpawnTimer = SpawnInterval;
    DifficultyTimer = 0.0f;
    CurrentWave = 1;
    CurrentDifficultyMultiplier = 1.0f;
}

void AProjectAscensionGameMode::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Update spawn timer
    SpawnTimer += DeltaTime;
    if (SpawnTimer >= SpawnInterval)
    {
        SpawnEnemyWave();
        SpawnTimer = 0.0f;
    }

    // Update difficulty timer
    DifficultyTimer += DeltaTime;
    if (DifficultyTimer >= DifficultyIncreaseInterval)
    {
        UpdateGameDifficulty();
        DifficultyTimer = 0.0f;
    }
}

void AProjectAscensionGameMode::SpawnEnemyWave()
{
    if (EnemyClasses.Num() == 0)
    {
        return;
    }

    ASurvivorsPlayerCharacter* PlayerCharacter = GetPlayerCharacter();
    if (!PlayerCharacter)
    {
        return;
    }

    // Spawn enemies for this wave
    const int32 EnemiesToSpawn = FMath::RoundToInt(EnemiesPerWave * CurrentDifficultyMultiplier);

    for (int32 i = 0; i < EnemiesToSpawn; i++)
    {
        // Pick random enemy class
        const int32 RandomIndex = FMath::RandRange(0, EnemyClasses.Num() - 1);
        TSubclassOf<ASurvivorsEnemyCharacter> EnemyClass = EnemyClasses[RandomIndex];

        if (EnemyClass)
        {
            FVector SpawnLocation = GetRandomSpawnLocation();
            SpawnEnemy(EnemyClass, SpawnLocation);
        }
    }

    CurrentWave++;
}

void AProjectAscensionGameMode::SpawnEnemy(TSubclassOf<ASurvivorsEnemyCharacter> EnemyClass, FVector SpawnLocation)
{
    if (!EnemyClass)
    {
        return;
    }

    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    ASurvivorsEnemyCharacter* SpawnedEnemy = GetWorld()->SpawnActor<ASurvivorsEnemyCharacter>(EnemyClass, SpawnLocation, FRotator::ZeroRotator, SpawnParams);

    if (SpawnedEnemy)
    {
        // Apply difficulty scaling to enemy
        // TODO: Scale enemy attributes based on CurrentDifficultyMultiplier
    }
}

FVector AProjectAscensionGameMode::GetRandomSpawnLocation() const
{
    ASurvivorsPlayerCharacter* PlayerCharacter = GetPlayerCharacter();
    if (!PlayerCharacter)
    {
        return FVector::ZeroVector;
    }

    const FVector PlayerLocation = PlayerCharacter->GetActorLocation();

    // Generate random angle
    const float RandomAngle = FMath::RandRange(0.0f, 360.0f);
    const float RadianAngle = FMath::DegreesToRadians(RandomAngle);

    // Generate random distance between MinSpawnDistance and SpawnRadius
    const float RandomDistance = FMath::RandRange(MinSpawnDistance, SpawnRadius);

    // Calculate spawn position
    const FVector SpawnOffset = FVector(
        FMath::Cos(RadianAngle) * RandomDistance,
        FMath::Sin(RadianAngle) * RandomDistance,
        0.0f
    );

    return PlayerLocation + SpawnOffset;
}

void AProjectAscensionGameMode::UpdateGameDifficulty()
{
    CurrentDifficultyMultiplier *= DifficultyMultiplier;

    // Optionally reduce spawn interval to increase spawn rate
    SpawnInterval = FMath::Max(0.5f, SpawnInterval * 0.95f);

    UE_LOG(LogTemp, Log, TEXT("Difficulty increased! Wave: %d, Multiplier: %f, Spawn Interval: %f"),
           CurrentWave, CurrentDifficultyMultiplier, SpawnInterval);
}

ASurvivorsPlayerCharacter* AProjectAscensionGameMode::GetPlayerCharacter() const
{
    if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
    {
        return Cast<ASurvivorsPlayerCharacter>(PC->GetPawn());
    }
    return nullptr;
}

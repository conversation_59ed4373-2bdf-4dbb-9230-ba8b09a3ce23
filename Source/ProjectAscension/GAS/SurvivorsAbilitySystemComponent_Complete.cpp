#include "SurvivorsAbilitySystemComponent.h"
#include "SurvivorsGameplayTags.h"
#include "AbilitySystemBlueprintLibrary.h"
#include "GameplayAbilitySpec.h"
#include "Engine/Engine.h"

USurvivorsAbilitySystemComponent::USurvivorsAbilitySystemComponent()
{
    // Enable replication
    SetIsReplicated(true);
    
    // Set replication mode for abilities
    ReplicationMode = EGameplayEffectReplicationMode::Mixed;
}

void USurvivorsAbilitySystemComponent::AbilityActorInfoSet()
{
    Super::AbilityActorInfoSet();

    // Cache the attribute set
    SurvivorsAttributeSet = GetSet<USurvivorsAttributeSet>();

    // Bind to effect applied delegate
    OnGameplayEffectAppliedDelegateToSelf.AddUObject(this, &USurvivorsAbilitySystemComponent::EffectApplied);

    // Apply startup effects if we're on the server
    if (GetOwnerRole() == ROLE_Authority)
    {
        ApplyStartupEffects();
    }
}

void USurvivorsAbilitySystemComponent::AddCharacterAbilities(const TArray<TSubclassOf<UGameplayAbility>>& StartupAbilities)
{
    if (GetOwnerRole() != ROLE_Authority || bStartupAbilitiesGiven)
    {
        return;
    }

    for (const TSubclassOf<UGameplayAbility>& AbilityClass : StartupAbilities)
    {
        if (AbilityClass)
        {
            FGameplayAbilitySpec AbilitySpec = FGameplayAbilitySpec(AbilityClass, 1);
            FGameplayAbilitySpecHandle AbilitySpecHandle = GiveAbility(AbilitySpec);

            // Map ability tag to spec handle for easy lookup
            if (const UGameplayAbility* AbilityCDO = AbilityClass->GetDefaultObject<UGameplayAbility>())
            {
                FGameplayTag AbilityTag = AbilityCDO->AbilityTags.First();
                if (AbilityTag.IsValid())
                {
                    AbilityTagToSpecHandle.Add(AbilityTag, AbilitySpecHandle);
                    SpecHandleToAbilityTag.Add(AbilitySpecHandle, AbilityTag);
                }
            }
        }
    }

    bStartupAbilitiesGiven = true;
    AbilitiesGivenDelegate.Broadcast();
}

void USurvivorsAbilitySystemComponent::AddCharacterPassiveAbilities(const TArray<TSubclassOf<UGameplayAbility>>& StartupPassiveAbilities)
{
    if (GetOwnerRole() != ROLE_Authority)
    {
        return;
    }

    for (const TSubclassOf<UGameplayAbility>& AbilityClass : StartupPassiveAbilities)
    {
        if (AbilityClass)
        {
            FGameplayAbilitySpec AbilitySpec = FGameplayAbilitySpec(AbilityClass, 1);
            FGameplayAbilitySpecHandle AbilitySpecHandle = GiveAbility(AbilitySpec);

            // Try to activate passive abilities immediately
            TryActivateAbility(AbilitySpecHandle);

            // Map ability tag to spec handle
            if (const UGameplayAbility* AbilityCDO = AbilityClass->GetDefaultObject<UGameplayAbility>())
            {
                FGameplayTag AbilityTag = AbilityCDO->AbilityTags.First();
                if (AbilityTag.IsValid())
                {
                    AbilityTagToSpecHandle.Add(AbilityTag, AbilitySpecHandle);
                    SpecHandleToAbilityTag.Add(AbilitySpecHandle, AbilityTag);
                }
            }
        }
    }
}

bool USurvivorsAbilitySystemComponent::TryActivateAbilityByTag(const FGameplayTag& AbilityTag, bool bAllowRemoteActivation)
{
    if (FGameplayAbilitySpecHandle* FoundHandle = AbilityTagToSpecHandle.Find(AbilityTag))
    {
        return TryActivateAbility(*FoundHandle, bAllowRemoteActivation);
    }
    return false;
}

void USurvivorsAbilitySystemComponent::AbilityInputTagPressed(const FGameplayTag& InputTag)
{
    if (!InputTag.IsValid())
    {
        return;
    }

    // Find abilities with matching input tags
    for (FGameplayAbilitySpec& AbilitySpec : GetActivatableAbilities())
    {
        if (AbilitySpec.Ability && AbilitySpec.DynamicAbilityTags.HasTagExact(InputTag))
        {
            AbilitySpecInputPressed(AbilitySpec);

            if (AbilitySpec.IsActive())
            {
                AbilitySpecInputPressed(AbilitySpec);
            }
            else
            {
                TryActivateAbility(AbilitySpec.Handle);
            }
        }
    }
}

void USurvivorsAbilitySystemComponent::AbilityInputTagReleased(const FGameplayTag& InputTag)
{
    if (!InputTag.IsValid())
    {
        return;
    }

    for (FGameplayAbilitySpec& AbilitySpec : GetActivatableAbilities())
    {
        if (AbilitySpec.Ability && AbilitySpec.DynamicAbilityTags.HasTagExact(InputTag) && AbilitySpec.IsActive())
        {
            AbilitySpecInputReleased(AbilitySpec);
        }
    }
}

void USurvivorsAbilitySystemComponent::AbilityInputTagHeld(const FGameplayTag& InputTag)
{
    if (!InputTag.IsValid())
    {
        return;
    }

    for (FGameplayAbilitySpec& AbilitySpec : GetActivatableAbilities())
    {
        if (AbilitySpec.Ability && AbilitySpec.DynamicAbilityTags.HasTagExact(InputTag) && AbilitySpec.IsActive())
        {
            AbilitySpecInputPressed(AbilitySpec);
        }
    }
}

FGameplayAbilitySpecHandle USurvivorsAbilitySystemComponent::GetAbilitySpecHandleFromTag(const FGameplayTag& AbilityTag)
{
    if (FGameplayAbilitySpecHandle* FoundHandle = AbilityTagToSpecHandle.Find(AbilityTag))
    {
        return *FoundHandle;
    }
    return FGameplayAbilitySpecHandle();
}

// Attribute Accessors
USurvivorsAttributeSet* USurvivorsAbilitySystemComponent::GetSurvivorsAttributeSet() const
{
    return const_cast<USurvivorsAttributeSet*>(SurvivorsAttributeSet);
}

int32 USurvivorsAbilitySystemComponent::GetCharacterLevel() const
{
    return SurvivorsAttributeSet ? FMath::FloorToInt(SurvivorsAttributeSet->GetLevel()) : 1;
}

float USurvivorsAbilitySystemComponent::GetExperience() const
{
    return SurvivorsAttributeSet ? SurvivorsAttributeSet->GetExperience() : 0.0f;
}

float USurvivorsAbilitySystemComponent::GetExperienceToNextLevel() const
{
    return SurvivorsAttributeSet ? SurvivorsAttributeSet->GetExperienceToNextLevel() : 100.0f;
}

float USurvivorsAbilitySystemComponent::GetExperiencePercent() const
{
    if (!SurvivorsAttributeSet)
    {
        return 0.0f;
    }
    
    const float ExpToNext = GetExperienceToNextLevel();
    return ExpToNext > 0.0f ? GetExperience() / ExpToNext : 0.0f;
}

float USurvivorsAbilitySystemComponent::GetHealth() const
{
    return SurvivorsAttributeSet ? SurvivorsAttributeSet->GetHealth() : 0.0f;
}

float USurvivorsAbilitySystemComponent::GetMaxHealth() const
{
    return SurvivorsAttributeSet ? SurvivorsAttributeSet->GetMaxHealth() : 0.0f;
}

float USurvivorsAbilitySystemComponent::GetHealthPercent() const
{
    if (!SurvivorsAttributeSet)
    {
        return 0.0f;
    }
    
    const float MaxHP = GetMaxHealth();
    return MaxHP > 0.0f ? GetHealth() / MaxHP : 0.0f;
}

float USurvivorsAbilitySystemComponent::GetAttackDamage() const
{
    return SurvivorsAttributeSet ? SurvivorsAttributeSet->GetAttackDamage() : 0.0f;
}

float USurvivorsAbilitySystemComponent::GetAttackSpeed() const
{
    return SurvivorsAttributeSet ? SurvivorsAttributeSet->GetAttackSpeed() : 1.0f;
}

float USurvivorsAbilitySystemComponent::GetAttackRange() const
{
    return SurvivorsAttributeSet ? SurvivorsAttributeSet->GetAttackRange() : 150.0f;
}

float USurvivorsAbilitySystemComponent::GetMovementSpeed() const
{
    return SurvivorsAttributeSet ? SurvivorsAttributeSet->GetMovementSpeed() : 300.0f;
}

FActiveGameplayEffectHandle USurvivorsAbilitySystemComponent::ApplyGameplayEffectSpecToSelfWithPrediction(const FGameplayEffectSpecHandle& SpecHandle, FPredictionKey PredictionKey)
{
    if (SpecHandle.IsValid())
    {
        return ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get(), PredictionKey);
    }
    return FActiveGameplayEffectHandle();
}

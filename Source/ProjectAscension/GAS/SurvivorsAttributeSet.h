#pragma once

#include "CoreMinimal.h"
#include "AttributeSet.h"
#include "AbilitySystemComponent.h"
#include "SurvivorsAttributeSet.generated.h"

// Uses macros from AttributeSet.h
#define ATTRIBUTE_ACCESSORS(ClassName, PropertyName) \
    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(ClassName, PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_GETTER(PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_SETTER(PropertyName) \
    GAMEPLAYATTRIBUTE_VALUE_INITTER(PropertyName)

/**
 * USurvivorsAttributeSet
 * 
 * Attribute set for the Survivors game containing all character stats.
 * Handles health, combat stats, movement, and progression attributes.
 */
UCLASS(BlueprintType)
class PROJECTASCENSION_API USurvivorsAttributeSet : public UAttributeSet
{
    GENERATED_BODY()

public:
    USurvivorsAttributeSet();

    // UAttributeSet interface
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
    virtual void PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue) override;
    virtual void PostGameplayEffectExecute(const FGameplayEffectModCallbackData& Data) override;

    // Health attributes
    UPROPERTY(BlueprintReadOnly, Category = "Health", ReplicatedUsing = OnRep_Health)
    FGameplayAttributeData Health;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, Health)

    UPROPERTY(BlueprintReadOnly, Category = "Health", ReplicatedUsing = OnRep_MaxHealth)
    FGameplayAttributeData MaxHealth;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, MaxHealth)

    UPROPERTY(BlueprintReadOnly, Category = "Health", ReplicatedUsing = OnRep_HealthRegeneration)
    FGameplayAttributeData HealthRegeneration;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, HealthRegeneration)

    // Combat attributes
    UPROPERTY(BlueprintReadOnly, Category = "Combat", ReplicatedUsing = OnRep_AttackDamage)
    FGameplayAttributeData AttackDamage;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, AttackDamage)

    UPROPERTY(BlueprintReadOnly, Category = "Combat", ReplicatedUsing = OnRep_AttackSpeed)
    FGameplayAttributeData AttackSpeed;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, AttackSpeed)

    UPROPERTY(BlueprintReadOnly, Category = "Combat", ReplicatedUsing = OnRep_AttackRange)
    FGameplayAttributeData AttackRange;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, AttackRange)

    UPROPERTY(BlueprintReadOnly, Category = "Combat", ReplicatedUsing = OnRep_CriticalHitChance)
    FGameplayAttributeData CriticalHitChance;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, CriticalHitChance)

    UPROPERTY(BlueprintReadOnly, Category = "Combat", ReplicatedUsing = OnRep_CriticalHitMultiplier)
    FGameplayAttributeData CriticalHitMultiplier;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, CriticalHitMultiplier)

    // Movement attributes
    UPROPERTY(BlueprintReadOnly, Category = "Movement", ReplicatedUsing = OnRep_MovementSpeed)
    FGameplayAttributeData MovementSpeed;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, MovementSpeed)

    // Progression attributes
    UPROPERTY(BlueprintReadOnly, Category = "Progression", ReplicatedUsing = OnRep_Experience)
    FGameplayAttributeData Experience;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, Experience)

    UPROPERTY(BlueprintReadOnly, Category = "Progression", ReplicatedUsing = OnRep_ExperienceToNextLevel)
    FGameplayAttributeData ExperienceToNextLevel;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, ExperienceToNextLevel)

    UPROPERTY(BlueprintReadOnly, Category = "Progression", ReplicatedUsing = OnRep_Level)
    FGameplayAttributeData Level;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, Level)

    // Meta attributes (not replicated, used for calculations)
    UPROPERTY(BlueprintReadOnly, Category = "Meta")
    FGameplayAttributeData IncomingDamage;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, IncomingDamage)

    UPROPERTY(BlueprintReadOnly, Category = "Meta")
    FGameplayAttributeData IncomingHealing;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, IncomingHealing)

    UPROPERTY(BlueprintReadOnly, Category = "Meta")
    FGameplayAttributeData IncomingExperience;
    ATTRIBUTE_ACCESSORS(USurvivorsAttributeSet, IncomingExperience)

protected:
    // Rep notifies
    UFUNCTION()
    virtual void OnRep_Health(const FGameplayAttributeData& OldHealth);

    UFUNCTION()
    virtual void OnRep_MaxHealth(const FGameplayAttributeData& OldMaxHealth);

    UFUNCTION()
    virtual void OnRep_HealthRegeneration(const FGameplayAttributeData& OldHealthRegeneration);

    UFUNCTION()
    virtual void OnRep_AttackDamage(const FGameplayAttributeData& OldAttackDamage);

    UFUNCTION()
    virtual void OnRep_AttackSpeed(const FGameplayAttributeData& OldAttackSpeed);

    UFUNCTION()
    virtual void OnRep_AttackRange(const FGameplayAttributeData& OldAttackRange);

    UFUNCTION()
    virtual void OnRep_MovementSpeed(const FGameplayAttributeData& OldMovementSpeed);

    UFUNCTION()
    virtual void OnRep_CriticalHitChance(const FGameplayAttributeData& OldCriticalHitChance);

    UFUNCTION()
    virtual void OnRep_CriticalHitMultiplier(const FGameplayAttributeData& OldCriticalHitMultiplier);

    UFUNCTION()
    virtual void OnRep_Experience(const FGameplayAttributeData& OldExperience);

    UFUNCTION()
    virtual void OnRep_ExperienceToNextLevel(const FGameplayAttributeData& OldExperienceToNextLevel);

    UFUNCTION()
    virtual void OnRep_Level(const FGameplayAttributeData& OldLevel);

    // Helper functions
    void AdjustAttributeForMaxChange(FGameplayAttributeData& AffectedAttribute, const FGameplayAttributeData& MaxAttribute, float NewMaxValue, const FGameplayAttribute& AffectedAttributeProperty);
};

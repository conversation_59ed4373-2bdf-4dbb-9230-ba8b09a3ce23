#pragma once

#include "CoreMinimal.h"
#include "AbilitySystemComponent.h"
#include "SurvivorsAbilitySystemComponent.generated.h"

class USurvivorsAttributeSet;

/**
 * USurvivorsAbilitySystemComponent
 * 
 * Custom Ability System Component for the Survivors game.
 * Provides additional functionality specific to the survivors-like gameplay.
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class PROJECTASCENSION_API USurvivorsAbilitySystemComponent : public UAbilitySystemComponent
{
    GENERATED_BODY()

public:
    USurvivorsAbilitySystemComponent();

    // UAbilitySystemComponent interface
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    // Attribute Set access
    UFUNCTION(BlueprintCallable, Category = "Survivors|Attributes")
    USurvivorsAttributeSet* GetSurvivorsAttributeSet() const;

    // Experience and Level management
    UFUNCTION(BlueprintCallable, Category = "Survivors|Experience")
    float GetExperienceToNextLevel() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Experience")
    float GetExperiencePercent() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Experience")
    void AddExperience(float ExperienceAmount);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Experience")
    bool TryLevelUp();

    // Ability management
    UFUNCTION(BlueprintCallable, Category = "Survivors|Abilities")
    void GrantAbilityWithLevel(TSubclassOf<UGameplayAbility> AbilityClass, int32 Level = 1);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Abilities")
    void GrantAbilitiesWithLevel(TArray<TSubclassOf<UGameplayAbility>> AbilityClasses, int32 Level = 1);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Abilities")
    bool TryActivateAbilityByTag(const FGameplayTag& AbilityTag);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Abilities")
    void CancelAbilityByTag(const FGameplayTag& AbilityTag);

    // Effect management
    UFUNCTION(BlueprintCallable, Category = "Survivors|Effects")
    FActiveGameplayEffectHandle ApplyGameplayEffectToSelf(TSubclassOf<UGameplayEffect> EffectClass, float Level = 1.0f);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Effects")
    FActiveGameplayEffectHandle ApplyGameplayEffectToTarget(TSubclassOf<UGameplayEffect> EffectClass, UAbilitySystemComponent* TargetASC, float Level = 1.0f);

    // Attribute queries
    UFUNCTION(BlueprintCallable, Category = "Survivors|Attributes")
    float GetAttributeValue(FGameplayAttribute Attribute) const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Attributes")
    float GetAttributeBaseValue(FGameplayAttribute Attribute) const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Attributes")
    void SetAttributeBaseValue(FGameplayAttribute Attribute, float NewValue);

    // Combat utilities
    UFUNCTION(BlueprintCallable, Category = "Survivors|Combat")
    void ApplyDamage(float DamageAmount, AActor* DamageSource = nullptr);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Combat")
    void ApplyHealing(float HealingAmount, AActor* HealingSource = nullptr);

    // Death handling
    UFUNCTION(BlueprintCallable, Category = "Survivors|Death")
    bool IsAlive() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Death")
    void HandleDeath();

    UFUNCTION(BlueprintCallable, Category = "Survivors|Death")
    void HandleRevive();

protected:
    // Initialize default attributes
    virtual void InitializeDefaultAttributes();

    // Initialize default abilities
    virtual void InitializeDefaultAbilities();

    // Level up processing
    virtual void ProcessLevelUp(int32 NewLevel);

    // Default attribute sets
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Attributes")
    TSubclassOf<USurvivorsAttributeSet> DefaultAttributeSetClass;

    // Default abilities to grant
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Abilities")
    TArray<TSubclassOf<UGameplayAbility>> DefaultAbilities;

    // Default effects to apply
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Effects")
    TArray<TSubclassOf<UGameplayEffect>> DefaultEffects;

private:
    // Cached attribute set reference
    UPROPERTY()
    TObjectPtr<USurvivorsAttributeSet> SurvivorsAttributeSet;

    // Track if we've initialized
    bool bHasInitialized = false;

    // Experience calculation
    float CalculateExperienceForLevel(int32 Level) const;

    // Base experience required for level 2
    UPROPERTY(EditDefaultsOnly, Category = "Survivors|Experience")
    float BaseExperienceRequired = 100.0f;

    // Experience scaling factor per level
    UPROPERTY(EditDefaultsOnly, Category = "Survivors|Experience")
    float ExperienceScalingFactor = 1.2f;
};

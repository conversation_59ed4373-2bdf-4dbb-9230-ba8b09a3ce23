#include "SurvivorsAbilitySystemComponent.h"
#include "SurvivorsAttributeSet.h"
#include "SurvivorsGameplayTags.h"
#include "GameplayEffect.h"
#include "Abilities/GameplayAbility.h"
#include "Engine/World.h"

USurvivorsAbilitySystemComponent::USurvivorsAbilitySystemComponent()
{
    PrimaryComponentTick.bCanEverTick = false;
    
    // Set replication mode
    SetIsReplicated(true);
    ReplicationMode = EGameplayEffectReplicationMode::Mixed;

    // Set default values
    BaseExperienceRequired = 100.0f;
    ExperienceScalingFactor = 1.2f;
}

void USurvivorsAbilitySystemComponent::BeginPlay()
{
    Super::BeginPlay();

    if (GetOwnerRole() == ROLE_Authority && !bHasInitialized)
    {
        InitializeDefaultAttributes();
        InitializeDefaultAbilities();
        bHasInitialized = true;
    }
}

void USurvivorsAbilitySystemComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    Super::EndPlay(EndPlayReason);
}

USurvivorsAttributeSet* USurvivorsAbilitySystemComponent::GetSurvivorsAttributeSet() const
{
    if (!SurvivorsAttributeSet)
    {
        // Try to find the attribute set
        SurvivorsAttributeSet = const_cast<USurvivorsAttributeSet*>(GetSet<USurvivorsAttributeSet>());
    }
    return SurvivorsAttributeSet;
}

float USurvivorsAbilitySystemComponent::GetExperienceToNextLevel() const
{
    const USurvivorsAttributeSet* AttributeSet = GetSurvivorsAttributeSet();
    if (!AttributeSet)
    {
        return BaseExperienceRequired;
    }

    return AttributeSet->GetExperienceToNextLevel();
}

float USurvivorsAbilitySystemComponent::GetExperiencePercent() const
{
    const USurvivorsAttributeSet* AttributeSet = GetSurvivorsAttributeSet();
    if (!AttributeSet)
    {
        return 0.0f;
    }

    const float CurrentExp = AttributeSet->GetExperience();
    const float ExpToNext = AttributeSet->GetExperienceToNextLevel();
    
    if (ExpToNext <= 0.0f)
    {
        return 1.0f;
    }

    return FMath::Clamp(CurrentExp / ExpToNext, 0.0f, 1.0f);
}

void USurvivorsAbilitySystemComponent::AddExperience(float ExperienceAmount)
{
    if (GetOwnerRole() != ROLE_Authority)
    {
        return;
    }

    USurvivorsAttributeSet* AttributeSet = GetSurvivorsAttributeSet();
    if (!AttributeSet)
    {
        return;
    }

    const float CurrentExp = AttributeSet->GetExperience();
    SetNumericAttributeBase(AttributeSet->GetExperienceAttribute(), CurrentExp + ExperienceAmount);

    // Check for level up
    TryLevelUp();
}

bool USurvivorsAbilitySystemComponent::TryLevelUp()
{
    if (GetOwnerRole() != ROLE_Authority)
    {
        return false;
    }

    USurvivorsAttributeSet* AttributeSet = GetSurvivorsAttributeSet();
    if (!AttributeSet)
    {
        return false;
    }

    const float CurrentExp = AttributeSet->GetExperience();
    const float ExpToNext = AttributeSet->GetExperienceToNextLevel();

    if (CurrentExp >= ExpToNext)
    {
        const int32 CurrentLevel = FMath::FloorToInt(AttributeSet->GetLevel());
        const int32 NewLevel = CurrentLevel + 1;

        // Set new level
        SetNumericAttributeBase(AttributeSet->GetLevelAttribute(), NewLevel);

        // Calculate new experience requirement
        const float NewExpRequired = CalculateExperienceForLevel(NewLevel + 1);
        SetNumericAttributeBase(AttributeSet->GetExperienceToNextLevelAttribute(), NewExpRequired);

        // Process level up
        ProcessLevelUp(NewLevel);

        return true;
    }

    return false;
}

void USurvivorsAbilitySystemComponent::GrantAbilityWithLevel(TSubclassOf<UGameplayAbility> AbilityClass, int32 Level)
{
    if (GetOwnerRole() != ROLE_Authority || !AbilityClass)
    {
        return;
    }

    FGameplayAbilitySpec AbilitySpec(AbilityClass, Level, INDEX_NONE, GetOwner());
    GiveAbility(AbilitySpec);
}

void USurvivorsAbilitySystemComponent::GrantAbilitiesWithLevel(TArray<TSubclassOf<UGameplayAbility>> AbilityClasses, int32 Level)
{
    for (TSubclassOf<UGameplayAbility> AbilityClass : AbilityClasses)
    {
        GrantAbilityWithLevel(AbilityClass, Level);
    }
}

bool USurvivorsAbilitySystemComponent::TryActivateAbilityByTag(const FGameplayTag& AbilityTag)
{
    return TryActivateAbilitiesByTag(FGameplayTagContainer(AbilityTag));
}

void USurvivorsAbilitySystemComponent::CancelAbilityByTag(const FGameplayTag& AbilityTag)
{
    CancelAbilities(&FGameplayTagContainer(AbilityTag));
}

FActiveGameplayEffectHandle USurvivorsAbilitySystemComponent::ApplyGameplayEffectToSelf(TSubclassOf<UGameplayEffect> EffectClass, float Level)
{
    if (!EffectClass)
    {
        return FActiveGameplayEffectHandle();
    }

    FGameplayEffectContextHandle EffectContext = MakeEffectContext();
    EffectContext.AddSourceObject(GetOwner());

    FGameplayEffectSpecHandle SpecHandle = MakeOutgoingSpec(EffectClass, Level, EffectContext);
    if (SpecHandle.IsValid())
    {
        return ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
    }

    return FActiveGameplayEffectHandle();
}

FActiveGameplayEffectHandle USurvivorsAbilitySystemComponent::ApplyGameplayEffectToTarget(TSubclassOf<UGameplayEffect> EffectClass, UAbilitySystemComponent* TargetASC, float Level)
{
    if (!EffectClass || !TargetASC)
    {
        return FActiveGameplayEffectHandle();
    }

    FGameplayEffectContextHandle EffectContext = MakeEffectContext();
    EffectContext.AddSourceObject(GetOwner());

    FGameplayEffectSpecHandle SpecHandle = MakeOutgoingSpec(EffectClass, Level, EffectContext);
    if (SpecHandle.IsValid())
    {
        return ApplyGameplayEffectSpecToTarget(*SpecHandle.Data.Get(), TargetASC);
    }

    return FActiveGameplayEffectHandle();
}

// Attribute queries
float USurvivorsAbilitySystemComponent::GetAttributeValue(FGameplayAttribute Attribute) const
{
    return GetNumericAttribute(Attribute);
}

float USurvivorsAbilitySystemComponent::GetAttributeBaseValue(FGameplayAttribute Attribute) const
{
    return GetNumericAttributeBase(Attribute);
}

void USurvivorsAbilitySystemComponent::SetAttributeBaseValue(FGameplayAttribute Attribute, float NewValue)
{
    SetNumericAttributeBase(Attribute, NewValue);
}

void USurvivorsAbilitySystemComponent::ApplyDamage(float DamageAmount, AActor* DamageSource)
{
    // Implementation will be added when we have damage effects
    UE_LOG(LogTemp, Log, TEXT("Applying %f damage"), DamageAmount);
}

void USurvivorsAbilitySystemComponent::ApplyHealing(float HealingAmount, AActor* HealingSource)
{
    // Implementation will be added when we have healing effects
    UE_LOG(LogTemp, Log, TEXT("Applying %f healing"), HealingAmount);
}

bool USurvivorsAbilitySystemComponent::IsAlive() const
{
    const USurvivorsAttributeSet* AttributeSet = GetSurvivorsAttributeSet();
    if (!AttributeSet)
    {
        return true;
    }

    return AttributeSet->GetHealth() > 0.0f;
}

void USurvivorsAbilitySystemComponent::HandleDeath()
{
    UE_LOG(LogTemp, Log, TEXT("Character died"));
    // Add death handling logic
}

void USurvivorsAbilitySystemComponent::HandleRevive()
{
    UE_LOG(LogTemp, Log, TEXT("Character revived"));
    // Add revive handling logic
}

void USurvivorsAbilitySystemComponent::InitializeDefaultAttributes()
{
    if (DefaultAttributeSetClass)
    {
        SurvivorsAttributeSet = NewObject<USurvivorsAttributeSet>(this, DefaultAttributeSetClass);
        AddAttributeSetSubobject(SurvivorsAttributeSet);
    }

    // Apply default effects
    for (TSubclassOf<UGameplayEffect> EffectClass : DefaultEffects)
    {
        if (EffectClass)
        {
            ApplyGameplayEffectToSelf(EffectClass, 1.0f);
        }
    }
}

void USurvivorsAbilitySystemComponent::InitializeDefaultAbilities()
{
    for (TSubclassOf<UGameplayAbility> AbilityClass : DefaultAbilities)
    {
        GrantAbilityWithLevel(AbilityClass, 1);
    }
}

void USurvivorsAbilitySystemComponent::ProcessLevelUp(int32 NewLevel)
{
    UE_LOG(LogTemp, Log, TEXT("Character leveled up to level %d"), NewLevel);
    
    // Add level up logic here
    // This could include stat increases, new abilities, etc.
}

float USurvivorsAbilitySystemComponent::CalculateExperienceForLevel(int32 Level) const
{
    if (Level <= 1)
    {
        return 0.0f;
    }

    return BaseExperienceRequired * FMath::Pow(ExperienceScalingFactor, Level - 2);
}

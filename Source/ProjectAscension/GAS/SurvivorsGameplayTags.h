#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"

/**
 * FSurvivorsGameplayTags
 * 
 * Singleton containing native gameplay tags for the Survivors game.
 * These tags are registered automatically and can be used throughout the project.
 */
struct PROJECTASCENSION_API FSurvivorsGameplayTags
{
public:
    static const FSurvivorsGameplayTags& Get() { return GameplayTags; }
    static void InitializeNativeTags();

    // Ability Tags
    FGameplayTag Ability_Movement;
    FGameplayTag Ability_AutoAttack;
    FGameplayTag Ability_Weapon_Projectile;
    FGameplayTag Ability_Weapon_Melee;
    FGameplayTag Ability_Dash;
    FGameplayTag Ability_AreaAttack;
    FGameplayTag Ability_Charge;
    FGameplayTag Ability_RageMode;

    // Character State Tags
    FGameplayTag Character_State_Dead;
    FGameplayTag Character_State_Stunned;
    FGameplayTag Character_State_Moving;
    FGameplayTag Character_State_Attacking;
    FGameplayTag Character_State_Invulnerable;

    // Enemy Type Tags
    FGameplayTag Enemy_Type_Basic;
    FGameplayTag Enemy_Type_Fast;
    FGameplayTag Enemy_Type_Tank;

    // Effect Tags
    FGameplayTag Effect_Damage;
    FGameplayTag Effect_Healing;
    FGameplayTag Effect_ExperienceGain;
    FGameplayTag Effect_StatBoost;

    // Damage Types
    FGameplayTag Damage_Physical;
    FGameplayTag Damage_Magical;
    FGameplayTag Damage_True;

    // Input Tags
    FGameplayTag Input_Move;
    FGameplayTag Input_Attack;
    FGameplayTag Input_Dash;
    FGameplayTag Input_Pause;

    // Weapon Tags
    FGameplayTag Weapon_Melee;
    FGameplayTag Weapon_Projectile;
    FGameplayTag Weapon_Area;

    // Upgrade Tags
    FGameplayTag Upgrade_Damage;
    FGameplayTag Upgrade_Speed;
    FGameplayTag Upgrade_Health;
    FGameplayTag Upgrade_Range;
    FGameplayTag Upgrade_CritChance;
    FGameplayTag Upgrade_NewWeapon;

protected:
    // Registers all of the tags with the GameplayTags Manager
    void AddAllTags(UGameplayTagsManager& Manager);

    // Helper function for adding a gameplay tag
    void AddTag(FGameplayTag& OutTag, const ANSICHAR* TagName, const ANSICHAR* TagComment);

private:
    static FSurvivorsGameplayTags GameplayTags;
};

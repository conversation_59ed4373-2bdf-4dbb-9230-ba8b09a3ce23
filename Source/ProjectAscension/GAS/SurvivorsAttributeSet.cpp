#include "SurvivorsAttributeSet.h"
#include "SurvivorsGameplayTags.h"
#include "Net/UnrealNetwork.h"
#include "GameplayEffect.h"
#include "GameplayEffectExtension.h"
#include "Engine/Engine.h"

USurvivorsAttributeSet::USurvivorsAttributeSet()
{
    // Initialize default values
    InitHealth(100.0f);
    InitMaxHealth(100.0f);
    InitHealthRegeneration(1.0f);
    InitAttackDamage(20.0f);
    InitAttackSpeed(1.0f);
    InitAttackRange(150.0f);
    InitMovementSpeed(300.0f);
    InitCriticalHitChance(0.05f);
    InitCriticalHitMultiplier(2.0f);
    InitExperience(0.0f);
    InitExperienceToNextLevel(100.0f);
    InitLevel(1.0f);
}

void USurvivorsAttributeSet::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, Health, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, MaxHealth, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, HealthRegeneration, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, AttackDamage, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, AttackSpeed, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, AttackRange, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, MovementSpeed, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, CriticalHitChance, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, CriticalHitMultiplier, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, Experience, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, ExperienceToNextLevel, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USurvivorsAttributeSet, Level, COND_None, REPNOTIFY_Always);
}

void USurvivorsAttributeSet::PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue)
{
    Super::PreAttributeChange(Attribute, NewValue);

    // Clamp values to valid ranges
    if (Attribute == GetHealthAttribute())
    {
        NewValue = FMath::Clamp(NewValue, 0.0f, GetMaxHealth());
    }
    else if (Attribute == GetMaxHealthAttribute())
    {
        NewValue = FMath::Max(NewValue, 1.0f);
    }
    else if (Attribute == GetAttackSpeedAttribute())
    {
        NewValue = FMath::Max(NewValue, 0.1f);
    }
    else if (Attribute == GetMovementSpeedAttribute())
    {
        NewValue = FMath::Max(NewValue, 0.0f);
    }
    else if (Attribute == GetCriticalHitChanceAttribute())
    {
        NewValue = FMath::Clamp(NewValue, 0.0f, 1.0f);
    }
    else if (Attribute == GetLevelAttribute())
    {
        NewValue = FMath::Max(NewValue, 1.0f);
    }
}

void USurvivorsAttributeSet::PostGameplayEffectExecute(const FGameplayEffectModCallbackData& Data)
{
    Super::PostGameplayEffectExecute(Data);

    FGameplayEffectContextHandle Context = Data.EffectSpec.GetContext();
    UAbilitySystemComponent* Source = Context.GetOriginalInstigatorAbilitySystemComponent();
    const FGameplayTagContainer& SourceTags = *Data.EffectSpec.CapturedSourceTags.GetAggregatedTags();

    // Get the Target actor, which should be our owner
    AActor* TargetActor = nullptr;
    AController* TargetController = nullptr;
    if (Data.Target.AbilityActorInfo.IsValid() && Data.Target.AbilityActorInfo->AvatarActor.IsValid())
    {
        TargetActor = Data.Target.AbilityActorInfo->AvatarActor.Get();
        TargetController = Data.Target.AbilityActorInfo->PlayerController.Get();
    }

    // Handle meta attributes
    if (Data.EvaluatedData.Attribute == GetIncomingDamageAttribute())
    {
        const float LocalIncomingDamage = GetIncomingDamage();
        SetIncomingDamage(0.0f);

        if (LocalIncomingDamage > 0.0f)
        {
            const float NewHealth = GetHealth() - LocalIncomingDamage;
            SetHealth(FMath::Clamp(NewHealth, 0.0f, GetMaxHealth()));
        }
    }
    else if (Data.EvaluatedData.Attribute == GetIncomingHealingAttribute())
    {
        const float LocalIncomingHealing = GetIncomingHealing();
        SetIncomingHealing(0.0f);

        if (LocalIncomingHealing > 0.0f)
        {
            const float NewHealth = GetHealth() + LocalIncomingHealing;
            SetHealth(FMath::Clamp(NewHealth, 0.0f, GetMaxHealth()));
        }
    }
    else if (Data.EvaluatedData.Attribute == GetIncomingExperienceAttribute())
    {
        const float LocalIncomingExperience = GetIncomingExperience();
        SetIncomingExperience(0.0f);

        if (LocalIncomingExperience > 0.0f)
        {
            const float NewExperience = GetExperience() + LocalIncomingExperience;
            SetExperience(NewExperience);
        }
    }

    // Handle max health changes
    if (Data.EvaluatedData.Attribute == GetMaxHealthAttribute())
    {
        AdjustAttributeForMaxChange(Health, MaxHealth, GetMaxHealth(), GetHealthAttribute());
    }
}

void USurvivorsAttributeSet::AdjustAttributeForMaxChange(FGameplayAttributeData& AffectedAttribute, const FGameplayAttributeData& MaxAttribute, float NewMaxValue, const FGameplayAttribute& AffectedAttributeProperty)
{
    UAbilitySystemComponent* AbilityComp = GetOwningAbilitySystemComponent();
    const float CurrentMaxValue = MaxAttribute.GetCurrentValue();
    if (!FMath::IsNearlyEqual(CurrentMaxValue, NewMaxValue) && AbilityComp)
    {
        const float CurrentValue = AffectedAttribute.GetCurrentValue();
        float NewDelta = (CurrentMaxValue > 0.0f) ? (CurrentValue * NewMaxValue / CurrentMaxValue) - CurrentValue : NewMaxValue;

        AbilityComp->ApplyModToAttributeUnsafe(AffectedAttributeProperty, EGameplayModOp::Additive, NewDelta);
    }
}

// Rep notify functions
void USurvivorsAttributeSet::OnRep_Health(const FGameplayAttributeData& OldHealth)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, Health, OldHealth);
}

void USurvivorsAttributeSet::OnRep_MaxHealth(const FGameplayAttributeData& OldMaxHealth)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, MaxHealth, OldMaxHealth);
}

void USurvivorsAttributeSet::OnRep_HealthRegeneration(const FGameplayAttributeData& OldHealthRegeneration)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, HealthRegeneration, OldHealthRegeneration);
}

void USurvivorsAttributeSet::OnRep_AttackDamage(const FGameplayAttributeData& OldAttackDamage)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, AttackDamage, OldAttackDamage);
}

void USurvivorsAttributeSet::OnRep_AttackSpeed(const FGameplayAttributeData& OldAttackSpeed)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, AttackSpeed, OldAttackSpeed);
}

void USurvivorsAttributeSet::OnRep_AttackRange(const FGameplayAttributeData& OldAttackRange)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, AttackRange, OldAttackRange);
}

void USurvivorsAttributeSet::OnRep_MovementSpeed(const FGameplayAttributeData& OldMovementSpeed)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, MovementSpeed, OldMovementSpeed);
}

void USurvivorsAttributeSet::OnRep_CriticalHitChance(const FGameplayAttributeData& OldCriticalHitChance)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, CriticalHitChance, OldCriticalHitChance);
}

void USurvivorsAttributeSet::OnRep_CriticalHitMultiplier(const FGameplayAttributeData& OldCriticalHitMultiplier)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, CriticalHitMultiplier, OldCriticalHitMultiplier);
}

void USurvivorsAttributeSet::OnRep_Experience(const FGameplayAttributeData& OldExperience)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, Experience, OldExperience);
}

void USurvivorsAttributeSet::OnRep_ExperienceToNextLevel(const FGameplayAttributeData& OldExperienceToNextLevel)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, ExperienceToNextLevel, OldExperienceToNextLevel);
}

void USurvivorsAttributeSet::OnRep_Level(const FGameplayAttributeData& OldLevel)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USurvivorsAttributeSet, Level, OldLevel);
}

#include "SurvivorsGameplayTags.h"
#include "GameplayTagsManager.h"
#include "Engine/Engine.h"

FSurvivorsGameplayTags FSurvivorsGameplayTags::GameplayTags;

void FSurvivorsGameplayTags::InitializeNativeTags()
{
    UGameplayTagsManager& GameplayTagsManager = UGameplayTagsManager::Get();
    GameplayTags.AddAllTags(GameplayTagsManager);

    // Notify manager that we are done adding native tags
    GameplayTagsManager.DoneAddingNativeTags();
}

void FSurvivorsGameplayTags::AddAllTags(UGameplayTagsManager& Manager)
{
    // Ability Tags
    AddTag(Ability_Movement, "Ability.Movement", "Movement ability tag");
    AddTag(Ability_AutoAttack, "Ability.AutoAttack", "Auto attack ability tag");
    AddTag(Ability_Weapon_Projectile, "Ability.Weapon.Projectile", "Projectile weapon ability tag");
    AddTag(Ability_Weapon_Melee, "Ability.Weapon.Melee", "Melee weapon ability tag");
    AddTag(Ability_Dash, "Ability.Dash", "Dash ability tag");
    AddTag(Ability_AreaAttack, "Ability.AreaAttack", "Area attack ability tag");
    AddTag(Ability_Charge, "Ability.Charge", "Charge ability tag");
    AddTag(Ability_RageMode, "Ability.RageMode", "Rage mode ability tag");

    // Character State Tags
    AddTag(Character_State_Dead, "Character.State.Dead", "Character is dead");
    AddTag(Character_State_Stunned, "Character.State.Stunned", "Character is stunned");
    AddTag(Character_State_Moving, "Character.State.Moving", "Character is moving");
    AddTag(Character_State_Attacking, "Character.State.Attacking", "Character is attacking");
    AddTag(Character_State_Invulnerable, "Character.State.Invulnerable", "Character is invulnerable");

    // Enemy Type Tags
    AddTag(Enemy_Type_Basic, "Enemy.Type.Basic", "Basic enemy type");
    AddTag(Enemy_Type_Fast, "Enemy.Type.Fast", "Fast enemy type");
    AddTag(Enemy_Type_Tank, "Enemy.Type.Tank", "Tank enemy type");

    // Effect Tags
    AddTag(Effect_Damage, "Effect.Damage", "Damage effect tag");
    AddTag(Effect_Healing, "Effect.Healing", "Healing effect tag");
    AddTag(Effect_ExperienceGain, "Effect.ExperienceGain", "Experience gain effect tag");
    AddTag(Effect_StatBoost, "Effect.StatBoost", "Stat boost effect tag");

    // Damage Types
    AddTag(Damage_Physical, "Damage.Physical", "Physical damage type");
    AddTag(Damage_Magical, "Damage.Magical", "Magical damage type");
    AddTag(Damage_True, "Damage.True", "True damage type");

    // Input Tags
    AddTag(Input_Move, "Input.Move", "Move input tag");
    AddTag(Input_Attack, "Input.Attack", "Attack input tag");
    AddTag(Input_Dash, "Input.Dash", "Dash input tag");
    AddTag(Input_Pause, "Input.Pause", "Pause input tag");

    // Weapon Tags
    AddTag(Weapon_Melee, "Weapon.Melee", "Melee weapon tag");
    AddTag(Weapon_Projectile, "Weapon.Projectile", "Projectile weapon tag");
    AddTag(Weapon_Area, "Weapon.Area", "Area weapon tag");

    // Upgrade Tags
    AddTag(Upgrade_Damage, "Upgrade.Damage", "Damage upgrade tag");
    AddTag(Upgrade_Speed, "Upgrade.Speed", "Speed upgrade tag");
    AddTag(Upgrade_Health, "Upgrade.Health", "Health upgrade tag");
    AddTag(Upgrade_Range, "Upgrade.Range", "Range upgrade tag");
    AddTag(Upgrade_CritChance, "Upgrade.CritChance", "Critical chance upgrade tag");
    AddTag(Upgrade_NewWeapon, "Upgrade.NewWeapon", "New weapon upgrade tag");
}

void FSurvivorsGameplayTags::AddTag(FGameplayTag& OutTag, const ANSICHAR* TagName, const ANSICHAR* TagComment)
{
    OutTag = UGameplayTagsManager::Get().AddNativeGameplayTag(FName(TagName), FString(TEXT("(Native) ")) + FString(TagComment));
}

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "ProjectAscensionGameMode.generated.h"

class ASurvivorsEnemyCharacter;
class ASurvivorsPlayerCharacter;

/**
 * AProjectAscensionGameMode
 *
 * Game mode for the Survivors-like game with wave-based enemy spawning,
 * experience progression, and GAS-based gameplay.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API AProjectAscensionGameMode : public AGameModeBase
{
    GENERATED_BODY()

public:
    AProjectAscensionGameMode();

    // Get current wave number
    UFUNCTION(BlueprintCallable, Category = "Survivors|Progression")
    int32 GetCurrentWave() const { return CurrentWave; }

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // Enemy spawning
    UFUNCTION(BlueprintCallable, Category = "Survivors|Spawning")
    void SpawnEnemyWave();

    UFUNCTION(BlueprintCallable, Category = "Survivors|Spawning")
    void SpawnEnemy(TSubclassOf<ASurvivorsEnemyCharacter> EnemyClass, FVector SpawnLocation);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Spawning")
    FVector GetRandomSpawnLocation() const;

    // Game progression
    UFUNCTION(BlueprintCallable, Category = "Survivors|Progression")
    void UpdateGameDifficulty();

    // Enemy spawning settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Spawning")
    TArray<TSubclassOf<ASurvivorsEnemyCharacter>> EnemyClasses;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Spawning")
    float SpawnInterval = 2.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Spawning")
    int32 EnemiesPerWave = 5;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Spawning")
    float SpawnRadius = 1500.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Spawning")
    float MinSpawnDistance = 800.0f;

    // Game progression settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Progression")
    float DifficultyIncreaseInterval = 30.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Progression")
    float DifficultyMultiplier = 1.1f;

private:
    // Spawning state
    float SpawnTimer = 0.0f;
    float DifficultyTimer = 0.0f;
    int32 CurrentWave = 1;
    float CurrentDifficultyMultiplier = 1.0f;

    // Get player character for spawn positioning
    ASurvivorsPlayerCharacter* GetPlayerCharacter() const;
};

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SphereComponent.h"
#include "GameFramework/ProjectileMovementComponent.h"
#include "SurvivorsProjectile.generated.h"

class UGameplayEffect;
class UStaticMeshComponent;
class UParticleSystemComponent;

/**
 * ASurvivorsProjectile
 * 
 * Base projectile class for the Survivors game.
 * Handles movement, collision, damage application, and visual effects.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API ASurvivorsProjectile : public AActor
{
    GENERATED_BODY()

public:
    ASurvivorsProjectile();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // Collision handling
    UFUNCTION()
    void OnHit(UPrimitiveComponent* HitComp, AActor* OtherActor, UPrimitiveComponent* OtherComp, FVector NormalImpulse, const FHitResult& Hit);

    UFUNCTION()
    void OnOverlapBegin(UPrimitiveComponent* OverlappedComp, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    // Damage application
    UFUNCTION(BlueprintCallable, Category = "Projectile")
    void ApplyDamageToTarget(AActor* Target);

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    bool IsValidTarget(AActor* Target) const;

    // Projectile lifecycle
    UFUNCTION(BlueprintCallable, Category = "Projectile")
    void DestroyProjectile();

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    void OnRangeExpired();

    // Visual effects
    UFUNCTION(BlueprintCallable, Category = "Projectile")
    void PlayImpactEffects(const FVector& ImpactLocation);

    // Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Projectile")
    TObjectPtr<USphereComponent> CollisionComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Projectile")
    TObjectPtr<UStaticMeshComponent> MeshComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Projectile")
    TObjectPtr<UProjectileMovementComponent> ProjectileMovement;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Projectile")
    TObjectPtr<UParticleSystemComponent> TrailEffect;

    // Projectile settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    float Damage = 10.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    float Speed = 1000.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    float Range = 800.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    float LifeTime = 5.0f;

    // Piercing settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    bool bCanPierce = false;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    int32 MaxPierceTargets = 3;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    int32 CurrentPierceCount = 0;

    // Damage effect
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Projectile")
    TSubclassOf<UGameplayEffect> DamageEffect;

    // Visual/Audio effects
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    UParticleSystem* ImpactEffect;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    USoundBase* ImpactSound;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Effects")
    USoundBase* FlybySound;

public:
    // Setters for configuring projectile
    UFUNCTION(BlueprintCallable, Category = "Projectile")
    void SetDamage(float NewDamage) { Damage = NewDamage; }

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    void SetSpeed(float NewSpeed);

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    void SetRange(float NewRange) { Range = NewRange; }

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    void SetPiercing(bool bPiercing, int32 MaxTargets = 3);

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    void SetDamageEffect(TSubclassOf<UGameplayEffect> NewDamageEffect) { DamageEffect = NewDamageEffect; }

    // Getters
    UFUNCTION(BlueprintCallable, Category = "Projectile")
    float GetDamage() const { return Damage; }

    UFUNCTION(BlueprintCallable, Category = "Projectile")
    bool CanPierce() const { return bCanPierce && CurrentPierceCount < MaxPierceTargets; }

private:
    // Tracking
    FVector StartLocation = FVector::ZeroVector;
    TArray<AActor*> HitTargets;

    // Timers
    FTimerHandle LifeTimeTimer;
    FTimerHandle RangeCheckTimer;

    // Check if projectile has traveled its maximum range
    void CheckRange();
};

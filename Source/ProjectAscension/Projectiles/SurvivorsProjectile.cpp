#include "SurvivorsProjectile.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "../Characters/SurvivorsCharacterBase.h"
#include "../Characters/SurvivorsPlayerCharacter.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "GameFramework/ProjectileMovementComponent.h"
#include "Particles/ParticleSystemComponent.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"
#include "Sound/SoundBase.h"
#include "Particles/ParticleSystem.h"
#include "AbilitySystemBlueprintLibrary.h"
#include "GameplayEffect.h"

ASurvivorsProjectile::ASurvivorsProjectile()
{
    PrimaryActorTick.bCanEverTick = true;

    // Set up collision component
    CollisionComponent = CreateDefaultSubobject<USphereComponent>(TEXT("CollisionComponent"));
    CollisionComponent->SetSphereRadius(5.0f);
    CollisionComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    CollisionComponent->SetCollisionResponseToAllChannels(ECR_Block);
    CollisionComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
    CollisionComponent->SetCollisionResponseToChannel(ECC_WorldStatic, ECR_Block);
    CollisionComponent->SetCollisionResponseToChannel(ECC_WorldDynamic, ECR_Block);
    RootComponent = CollisionComponent;

    // Set up mesh component
    MeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MeshComponent"));
    MeshComponent->SetupAttachment(RootComponent);
    MeshComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);

    // Set up projectile movement
    ProjectileMovement = CreateDefaultSubobject<UProjectileMovementComponent>(TEXT("ProjectileMovement"));
    ProjectileMovement->UpdatedComponent = CollisionComponent;
    ProjectileMovement->InitialSpeed = Speed;
    ProjectileMovement->MaxSpeed = Speed;
    ProjectileMovement->bRotationFollowsVelocity = true;
    ProjectileMovement->bShouldBounce = false;
    ProjectileMovement->ProjectileGravityScale = 0.0f;

    // Set up trail effect
    TrailEffect = CreateDefaultSubobject<UParticleSystemComponent>(TEXT("TrailEffect"));
    TrailEffect->SetupAttachment(RootComponent);

    // Set default values
    Damage = 10.0f;
    Speed = 1000.0f;
    Range = 800.0f;
    LifeTime = 5.0f;
    bCanPierce = false;
    MaxPierceTargets = 3;
    CurrentPierceCount = 0;

    // Enable replication
    SetReplicates(true);
    SetReplicateMovement(true);
}

void ASurvivorsProjectile::BeginPlay()
{
    Super::BeginPlay();

    // Store starting location
    StartLocation = GetActorLocation();

    // Bind collision events
    CollisionComponent->OnComponentHit.AddDynamic(this, &ASurvivorsProjectile::OnHit);
    CollisionComponent->OnComponentBeginOverlap.AddDynamic(this, &ASurvivorsProjectile::OnOverlapBegin);

    // Set up lifetime timer
    GetWorldTimerManager().SetTimer(LifeTimeTimer, this, &ASurvivorsProjectile::DestroyProjectile, LifeTime, false);

    // Set up range check timer
    GetWorldTimerManager().SetTimer(RangeCheckTimer, this, &ASurvivorsProjectile::CheckRange, 0.1f, true);
}

void ASurvivorsProjectile::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Additional tick logic if needed
}

void ASurvivorsProjectile::OnHit(UPrimitiveComponent* HitComp, AActor* OtherActor, UPrimitiveComponent* OtherComp, FVector NormalImpulse, const FHitResult& Hit)
{
    if (!OtherActor || OtherActor == GetOwner())
    {
        return;
    }

    // Check if it's a valid target
    if (IsValidTarget(OtherActor))
    {
        ApplyDamageToTarget(OtherActor);
        
        // Add to hit targets to prevent multiple hits
        HitTargets.AddUnique(OtherActor);
        
        // Check piercing
        if (bCanPierce && CurrentPierceCount < MaxPierceTargets)
        {
            CurrentPierceCount++;
            return; // Continue through target
        }
    }

    // Play impact effects
    PlayImpactEffects(Hit.Location);

    // Destroy projectile
    DestroyProjectile();
}

void ASurvivorsProjectile::OnOverlapBegin(UPrimitiveComponent* OverlappedComp, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    // Handle overlap for piercing projectiles
    if (bCanPierce && OtherActor && OtherActor != GetOwner() && !HitTargets.Contains(OtherActor))
    {
        if (IsValidTarget(OtherActor))
        {
            ApplyDamageToTarget(OtherActor);
            HitTargets.AddUnique(OtherActor);
            CurrentPierceCount++;

            if (CurrentPierceCount >= MaxPierceTargets)
            {
                DestroyProjectile();
            }
        }
    }
}

void ASurvivorsProjectile::ApplyDamageToTarget(AActor* Target)
{
    if (!Target || !DamageEffect)
    {
        return;
    }

    UAbilitySystemComponent* TargetASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(Target);
    if (!TargetASC)
    {
        return;
    }

    UAbilitySystemComponent* SourceASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(GetOwner());
    if (!SourceASC)
    {
        return;
    }

    // Create damage effect spec
    FGameplayEffectContextHandle EffectContext = SourceASC->MakeEffectContext();
    EffectContext.AddSourceObject(GetOwner());
    EffectContext.AddInstigator(GetOwner(), this);

    FGameplayEffectSpecHandle DamageSpecHandle = SourceASC->MakeOutgoingSpec(DamageEffect, 1.0f, EffectContext);
    if (DamageSpecHandle.IsValid())
    {
        // Set damage magnitude
        DamageSpecHandle.Data->SetSetByCallerMagnitude(FSurvivorsGameplayTags::Get().Effect_Damage, Damage);

        // Apply the damage effect
        SourceASC->ApplyGameplayEffectSpecToTarget(*DamageSpecHandle.Data.Get(), TargetASC);
    }
}

bool ASurvivorsProjectile::IsValidTarget(AActor* Target) const
{
    if (!Target || Target == GetOwner())
    {
        return false;
    }

    // Check if already hit this target
    if (HitTargets.Contains(Target))
    {
        return false;
    }

    // Check if target is alive
    if (ASurvivorsCharacterBase* TargetCharacter = Cast<ASurvivorsCharacterBase>(Target))
    {
        if (!TargetCharacter->IsAlive())
        {
            return false;
        }

        // Player projectiles hit enemies, enemy projectiles hit players
        if (GetOwner() && GetOwner()->IsA<ASurvivorsPlayerCharacter>())
        {
            // Player projectile - hit enemies (not other players)
            return !Target->IsA<ASurvivorsPlayerCharacter>();
        }
        else
        {
            // Enemy projectile - hit players
            return Target->IsA<ASurvivorsPlayerCharacter>();
        }
    }

    return false;
}

void ASurvivorsProjectile::DestroyProjectile()
{
    // Clear timers
    GetWorldTimerManager().ClearTimer(LifeTimeTimer);
    GetWorldTimerManager().ClearTimer(RangeCheckTimer);

    // Play impact effects at current location
    PlayImpactEffects(GetActorLocation());

    // Destroy the actor
    Destroy();
}

void ASurvivorsProjectile::OnRangeExpired()
{
    DestroyProjectile();
}

void ASurvivorsProjectile::PlayImpactEffects(const FVector& ImpactLocation)
{
    // Play impact sound
    if (ImpactSound)
    {
        UGameplayStatics::PlaySoundAtLocation(GetWorld(), ImpactSound, ImpactLocation);
    }

    // Play impact particle effect
    if (ImpactEffect)
    {
        UGameplayStatics::SpawnEmitterAtLocation(GetWorld(), ImpactEffect, ImpactLocation);
    }
}

void ASurvivorsProjectile::SetSpeed(float NewSpeed)
{
    Speed = NewSpeed;
    if (ProjectileMovement)
    {
        ProjectileMovement->InitialSpeed = Speed;
        ProjectileMovement->MaxSpeed = Speed;
    }
}

void ASurvivorsProjectile::SetPiercing(bool bPiercing, int32 MaxTargets)
{
    bCanPierce = bPiercing;
    MaxPierceTargets = MaxTargets;
    
    if (bCanPierce)
    {
        // Enable overlap events for piercing
        CollisionComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
    }
    else
    {
        // Use blocking collision for non-piercing
        CollisionComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
    }
}

void ASurvivorsProjectile::CheckRange()
{
    const float DistanceTraveled = FVector::Dist(StartLocation, GetActorLocation());
    if (DistanceTraveled >= Range)
    {
        OnRangeExpired();
    }
}

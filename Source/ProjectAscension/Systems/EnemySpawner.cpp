#include "EnemySpawner.h"
#include "../Characters/SurvivorsEnemyCharacter.h"
#include "../Characters/SurvivorsPlayerCharacter.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "NavigationSystem.h"

UEnemySpawner::UEnemySpawner()
{
    PrimaryComponentTick.bCanEverTick = true;

    // Set default values
    BaseSpawnInterval = 2.0f;
    BaseEnemiesPerWave = 5;
    SpawnRadius = 1500.0f;
    MinSpawnDistance = 800.0f;
    MaxConcurrentEnemies = 50;
    DifficultyIncreaseInterval = 30.0f;
    DifficultyMultiplierPerWave = 1.1f;
    MaxDifficultyMultiplier = 5.0f;
}

void UEnemySpawner::BeginPlay()
{
    Super::BeginPlay();

    // Cache player character
    PlayerCharacter = GetPlayerCharacter();
}

void UEnemySpawner::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (bIsSpawning && !bIsPaused)
    {
        UpdateSpawning(DeltaTime);
        UpdateDifficulty(DeltaTime);
        CleanupDeadEnemies();
    }
}

void UEnemySpawner::StartSpawning()
{
    bIsSpawning = true;
    bIsPaused = false;
    CurrentWave = 1;
    CurrentDifficultyMultiplier = 1.0f;
    
    StartNextWave();
    
    UE_LOG(LogTemp, Log, TEXT("Enemy spawning started"));
}

void UEnemySpawner::StopSpawning()
{
    bIsSpawning = false;
    bIsPaused = false;
    
    UE_LOG(LogTemp, Log, TEXT("Enemy spawning stopped"));
}

void UEnemySpawner::PauseSpawning()
{
    bIsPaused = true;
    UE_LOG(LogTemp, Log, TEXT("Enemy spawning paused"));
}

void UEnemySpawner::ResumeSpawning()
{
    bIsPaused = false;
    UE_LOG(LogTemp, Log, TEXT("Enemy spawning resumed"));
}

void UEnemySpawner::StartNextWave()
{
    EnemiesSpawnedThisWave = 0;
    
    FSpawnWaveData WaveData = GetCurrentWaveData();
    EnemiesRemainingThisWave = WaveData.EnemyCount;
    
    OnWaveStarted.Broadcast(CurrentWave);
    
    UE_LOG(LogTemp, Log, TEXT("Starting wave %d with %d enemies"), CurrentWave, EnemiesRemainingThisWave);
}

void UEnemySpawner::ForceCompleteCurrentWave()
{
    EnemiesRemainingThisWave = 0;
    ProcessWaveCompletion();
}

void UEnemySpawner::UpdateSpawning(float DeltaTime)
{
    if (EnemiesRemainingThisWave <= 0)
    {
        ProcessWaveCompletion();
        return;
    }

    SpawnTimer += DeltaTime;
    
    FSpawnWaveData WaveData = GetCurrentWaveData();
    const float SpawnInterval = WaveData.SpawnInterval / CurrentDifficultyMultiplier;
    
    if (SpawnTimer >= SpawnInterval)
    {
        if (GetActiveEnemyCount() < MaxConcurrentEnemies)
        {
            TSubclassOf<ASurvivorsEnemyCharacter> EnemyClass = SelectEnemyToSpawn();
            if (EnemyClass)
            {
                FVector SpawnLocation = GetSpawnLocation();
                ASurvivorsEnemyCharacter* SpawnedEnemy = SpawnEnemy(EnemyClass, SpawnLocation);
                
                if (SpawnedEnemy)
                {
                    EnemiesSpawnedThisWave++;
                    EnemiesRemainingThisWave--;
                    OnEnemySpawned.Broadcast(SpawnedEnemy, CurrentWave);
                }
            }
        }
        
        SpawnTimer = 0.0f;
    }
}

ASurvivorsEnemyCharacter* UEnemySpawner::SpawnEnemy(TSubclassOf<ASurvivorsEnemyCharacter> EnemyClass, const FVector& SpawnLocation)
{
    if (!EnemyClass || !GetWorld())
    {
        return nullptr;
    }

    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    ASurvivorsEnemyCharacter* SpawnedEnemy = GetWorld()->SpawnActor<ASurvivorsEnemyCharacter>(EnemyClass, SpawnLocation, FRotator::ZeroRotator, SpawnParams);
    
    if (SpawnedEnemy)
    {
        SpawnedEnemies.Add(SpawnedEnemy);
        UE_LOG(LogTemp, Log, TEXT("Spawned enemy at location: %s"), *SpawnLocation.ToString());
    }

    return SpawnedEnemy;
}

TArray<ASurvivorsEnemyCharacter*> UEnemySpawner::SpawnEnemyGroup(const TArray<TSubclassOf<ASurvivorsEnemyCharacter>>& EnemyClasses, const FVector& CenterLocation, float Radius)
{
    TArray<ASurvivorsEnemyCharacter*> SpawnedGroup;

    for (int32 i = 0; i < EnemyClasses.Num(); i++)
    {
        // Calculate spawn position in circle around center
        float Angle = (2.0f * PI * i) / EnemyClasses.Num();
        FVector Offset = FVector(FMath::Cos(Angle) * Radius, FMath::Sin(Angle) * Radius, 0.0f);
        FVector SpawnLocation = CenterLocation + Offset;

        ASurvivorsEnemyCharacter* SpawnedEnemy = SpawnEnemy(EnemyClasses[i], SpawnLocation);
        if (SpawnedEnemy)
        {
            SpawnedGroup.Add(SpawnedEnemy);
        }
    }

    return SpawnedGroup;
}

TSubclassOf<ASurvivorsEnemyCharacter> UEnemySpawner::SelectEnemyToSpawn() const
{
    TArray<FEnemySpawnData> ValidSpawns;

    // Use wave-specific spawns if available
    FSpawnWaveData WaveData = GetCurrentWaveData();
    if (WaveData.SpecialSpawns.Num() > 0)
    {
        for (const FEnemySpawnData& SpawnData : WaveData.SpecialSpawns)
        {
            if (CanSpawnEnemy(SpawnData))
            {
                ValidSpawns.Add(SpawnData);
            }
        }
    }
    else
    {
        // Use default spawns
        for (const FEnemySpawnData& SpawnData : DefaultSpawnData)
        {
            if (CanSpawnEnemy(SpawnData))
            {
                ValidSpawns.Add(SpawnData);
            }
        }
    }

    if (ValidSpawns.Num() == 0)
    {
        return nullptr;
    }

    // Weighted random selection
    float TotalWeight = 0.0f;
    for (const FEnemySpawnData& SpawnData : ValidSpawns)
    {
        TotalWeight += SpawnData.SpawnWeight;
    }

    float RandomValue = FMath::RandRange(0.0f, TotalWeight);
    float CurrentWeight = 0.0f;

    for (const FEnemySpawnData& SpawnData : ValidSpawns)
    {
        CurrentWeight += SpawnData.SpawnWeight;
        if (RandomValue <= CurrentWeight)
        {
            return SpawnData.EnemyClass;
        }
    }

    // Fallback to first valid spawn
    return ValidSpawns[0].EnemyClass;
}

FVector UEnemySpawner::GetSpawnLocation() const
{
    if (!PlayerCharacter)
    {
        return FVector::ZeroVector;
    }

    FVector PlayerLocation = PlayerCharacter->GetActorLocation();
    
    // Try to find a valid spawn location
    for (int32 Attempts = 0; Attempts < 10; Attempts++)
    {
        // Random angle around player
        float Angle = FMath::RandRange(0.0f, 2.0f * PI);
        float Distance = FMath::RandRange(MinSpawnDistance, SpawnRadius);
        
        FVector SpawnLocation = PlayerLocation + FVector(
            FMath::Cos(Angle) * Distance,
            FMath::Sin(Angle) * Distance,
            0.0f
        );

        // Try to find ground
        FHitResult HitResult;
        FVector StartTrace = SpawnLocation + FVector(0.0f, 0.0f, 1000.0f);
        FVector EndTrace = SpawnLocation - FVector(0.0f, 0.0f, 1000.0f);
        
        if (GetWorld()->LineTraceSingleByChannel(HitResult, StartTrace, EndTrace, ECC_WorldStatic))
        {
            return HitResult.Location;
        }
    }

    // Fallback to offset from player
    return PlayerLocation + FVector(MinSpawnDistance, 0.0f, 0.0f);
}

bool UEnemySpawner::CanSpawnEnemy(const FEnemySpawnData& SpawnData) const
{
    if (!SpawnData.EnemyClass)
    {
        return false;
    }

    // Check player level requirements
    if (PlayerCharacter)
    {
        int32 PlayerLevel = PlayerCharacter->GetCharacterLevel();
        if (PlayerLevel < SpawnData.MinPlayerLevel || PlayerLevel > SpawnData.MaxPlayerLevel)
        {
            return false;
        }
    }

    // Check wave requirements
    if (CurrentWave < SpawnData.MinWave || CurrentWave > SpawnData.MaxWave)
    {
        return false;
    }

    // Check concurrent spawn limits
    if (SpawnData.MaxConcurrentSpawns > 0)
    {
        int32 CurrentCount = 0;
        for (const TWeakObjectPtr<ASurvivorsEnemyCharacter>& Enemy : SpawnedEnemies)
        {
            if (Enemy.IsValid() && Enemy->GetClass() == SpawnData.EnemyClass)
            {
                CurrentCount++;
            }
        }
        
        if (CurrentCount >= SpawnData.MaxConcurrentSpawns)
        {
            return false;
        }
    }

    // Check cooldown
    if (SpawnData.SpawnCooldown > 0.0f)
    {
        const float* LastSpawnTime = LastSpawnTimes.Find(SpawnData.EnemyClass);
        if (LastSpawnTime && (GetWorld()->GetTimeSeconds() - *LastSpawnTime) < SpawnData.SpawnCooldown)
        {
            return false;
        }
    }

    return true;
}

int32 UEnemySpawner::GetActiveEnemyCount() const
{
    int32 Count = 0;
    for (const TWeakObjectPtr<ASurvivorsEnemyCharacter>& Enemy : SpawnedEnemies)
    {
        if (Enemy.IsValid())
        {
            Count++;
        }
    }
    return Count;
}

void UEnemySpawner::ProcessWaveCompletion()
{
    if (GetActiveEnemyCount() == 0 && EnemiesRemainingThisWave <= 0)
    {
        OnWaveCompleted.Broadcast(CurrentWave);
        
        CurrentWave++;
        CurrentDifficultyMultiplier = FMath::Min(CurrentDifficultyMultiplier * DifficultyMultiplierPerWave, MaxDifficultyMultiplier);
        
        // Start next wave after a short delay
        FTimerHandle WaveDelayTimer;
        GetWorld()->GetTimerManager().SetTimer(WaveDelayTimer, this, &UEnemySpawner::StartNextWave, 3.0f, false);
    }
}

FSpawnWaveData UEnemySpawner::GetCurrentWaveData() const
{
    // Find specific wave configuration
    for (const FSpawnWaveData& WaveData : WaveConfiguration)
    {
        if (WaveData.WaveNumber == CurrentWave)
        {
            return WaveData;
        }
    }

    // Return default wave data
    FSpawnWaveData DefaultWave;
    DefaultWave.WaveNumber = CurrentWave;
    DefaultWave.EnemyCount = BaseEnemiesPerWave + (CurrentWave - 1) * 2; // Increase enemies per wave
    DefaultWave.SpawnInterval = BaseSpawnInterval;
    DefaultWave.DifficultyMultiplier = CurrentDifficultyMultiplier;
    
    return DefaultWave;
}

ASurvivorsPlayerCharacter* UEnemySpawner::GetPlayerCharacter()
{
    if (!PlayerCharacter)
    {
        if (APawn* PlayerPawn = UGameplayStatics::GetPlayerPawn(GetWorld(), 0))
        {
            PlayerCharacter = Cast<ASurvivorsPlayerCharacter>(PlayerPawn);
        }
    }
    return PlayerCharacter;
}

void UEnemySpawner::CleanupDeadEnemies()
{
    SpawnedEnemies.RemoveAll([](const TWeakObjectPtr<ASurvivorsEnemyCharacter>& Enemy)
    {
        return !Enemy.IsValid();
    });
}

void UEnemySpawner::UpdateDifficulty(float DeltaTime)
{
    DifficultyTimer += DeltaTime;
    
    if (DifficultyTimer >= DifficultyIncreaseInterval)
    {
        CurrentDifficultyMultiplier = FMath::Min(CurrentDifficultyMultiplier * 1.05f, MaxDifficultyMultiplier);
        DifficultyTimer = 0.0f;
        
        UE_LOG(LogTemp, Log, TEXT("Difficulty increased to %f"), CurrentDifficultyMultiplier);
    }
}

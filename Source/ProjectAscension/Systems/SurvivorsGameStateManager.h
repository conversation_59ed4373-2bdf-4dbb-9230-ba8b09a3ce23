#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "SurvivorsGameStateManager.generated.h"

UENUM(BlueprintType)
enum class ESurvivorsGameState : uint8
{
    MainMenu,
    Playing,
    Paused,
    LevelUp,
    GameOver,
    Victory
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnGameStateChanged, ESurvivorsGameState, OldState, ESurvivorsGameState, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGamePaused, bool, bIsPaused);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerDeath, class ASurvivorsPlayerCharacter*, DeadPlayer);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnVictoryAchieved, float, SurvivalTime);

/**
 * USurvivorsGameStateManager
 * 
 * Manages the overall game state and flow for the Survivors game.
 * Handles transitions between different game states and coordinates game events.
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class PROJECTASCENSION_API USurvivorsGameStateManager : public UActorComponent
{
    GENERATED_BODY()

public:
    USurvivorsGameStateManager();

    // Delegates
    UPROPERTY(BlueprintAssignable, Category = "Survivors|GameState")
    FOnGameStateChanged OnGameStateChanged;

    UPROPERTY(BlueprintAssignable, Category = "Survivors|GameState")
    FOnGamePaused OnGamePaused;

    UPROPERTY(BlueprintAssignable, Category = "Survivors|GameState")
    FOnPlayerDeath OnPlayerDeath;

    UPROPERTY(BlueprintAssignable, Category = "Survivors|GameState")
    FOnVictoryAchieved OnVictoryAchieved;

    // Game state management
    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void SetGameState(ESurvivorsGameState NewState);

    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    ESurvivorsGameState GetCurrentGameState() const { return CurrentGameState; }

    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    bool IsGameState(ESurvivorsGameState StateToCheck) const { return CurrentGameState == StateToCheck; }

    // Game flow control
    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void StartGame();

    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void PauseGame();

    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void ResumeGame();

    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void RestartGame();

    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void EndGame(bool bVictory = false);

    // Level up state
    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void EnterLevelUpState();

    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void ExitLevelUpState();

    // Game statistics
    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    float GetGameTime() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    float GetSurvivalTime() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    bool IsGameActive() const;

    // Player management
    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void RegisterPlayerDeath(class ASurvivorsPlayerCharacter* DeadPlayer);

    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void CheckVictoryConditions();

protected:
    virtual void BeginPlay() override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Handle state transitions
    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void OnEnterGameState(ESurvivorsGameState NewState);

    UFUNCTION(BlueprintCallable, Category = "Survivors|GameState")
    void OnExitGameState(ESurvivorsGameState OldState);

    // Victory conditions
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Victory")
    float VictoryTime = 600.0f; // 10 minutes

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Victory")
    int32 VictoryWaves = 20;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Victory")
    bool bTimeBasedVictory = true;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Victory")
    bool bWaveBasedVictory = false;

    // Game state tags
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|GameState")
    FGameplayTagContainer GameStateTags;

private:
    // Current game state
    ESurvivorsGameState CurrentGameState = ESurvivorsGameState::MainMenu;
    ESurvivorsGameState PreviousGameState = ESurvivorsGameState::MainMenu;

    // Game timing
    float GameStartTime = 0.0f;
    float PauseStartTime = 0.0f;
    float TotalPausedTime = 0.0f;

    // Game state tracking
    bool bGameStarted = false;
    bool bGameEnded = false;
    bool bIsPaused = false;

    // Cached references
    UPROPERTY()
    TObjectPtr<class ASurvivorsPlayerCharacter> PlayerCharacter;

    UPROPERTY()
    TObjectPtr<class AProjectAscensionGameMode> GameMode;

    // Get player character
    class ASurvivorsPlayerCharacter* GetPlayerCharacter();

    // Get game mode
    class AProjectAscensionGameMode* GetGameMode();

    // Apply game state effects
    void ApplyGameStateEffects(ESurvivorsGameState State);

    // Update game state tags
    void UpdateGameStateTags();
};

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "ExperienceManager.generated.h"

class UGameplayAbility;
class UGameplayEffect;
class ASurvivorsPlayerCharacter;

USTRUCT(BlueprintType)
struct FAbilityUpgradeOption
{
    GENERATED_BODY()

    // The ability class to grant or upgrade
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
    TSubclassOf<UGameplayAbility> AbilityClass;

    // Display information
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
    FText DisplayName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
    FText Description;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
    UTexture2D* Icon;

    // Upgrade tags for categorization
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
    FGameplayTagContainer UpgradeTags;

    // Requirements
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
    int32 RequiredLevel = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
    FGameplayTagContainer RequiredTags;

    // Stat bonuses (alternative to abilities)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
    TSubclassOf<UGameplayEffect> StatBoostEffect;

    FAbilityUpgradeOption()
    {
        AbilityClass = nullptr;
        DisplayName = FText::FromString("Unknown Upgrade");
        Description = FText::FromString("No description available");
        Icon = nullptr;
        RequiredLevel = 1;
        StatBoostEffect = nullptr;
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLevelUp, int32, NewLevel);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnExperienceGained, float, ExperienceGained, float, TotalExperience);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnUpgradeOptionsAvailable, const TArray<FAbilityUpgradeOption>&, UpgradeOptions);

/**
 * UExperienceManager
 * 
 * Manages experience gain, level progression, and ability upgrades for the Survivors game.
 * Handles the level-up process and presents upgrade choices to the player.
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class PROJECTASCENSION_API UExperienceManager : public UActorComponent
{
    GENERATED_BODY()

public:
    UExperienceManager();

    // Delegates
    UPROPERTY(BlueprintAssignable, Category = "Experience")
    FOnLevelUp OnLevelUp;

    UPROPERTY(BlueprintAssignable, Category = "Experience")
    FOnExperienceGained OnExperienceGained;

    UPROPERTY(BlueprintAssignable, Category = "Experience")
    FOnUpgradeOptionsAvailable OnUpgradeOptionsAvailable;

    // Experience management
    UFUNCTION(BlueprintCallable, Category = "Experience")
    void AddExperience(float Amount);

    UFUNCTION(BlueprintCallable, Category = "Experience")
    void TriggerLevelUp();

    UFUNCTION(BlueprintCallable, Category = "Experience")
    void SelectUpgrade(int32 UpgradeIndex);

    // Getters
    UFUNCTION(BlueprintCallable, Category = "Experience")
    int32 GetCurrentLevel() const;

    UFUNCTION(BlueprintCallable, Category = "Experience")
    float GetCurrentExperience() const;

    UFUNCTION(BlueprintCallable, Category = "Experience")
    float GetExperienceToNextLevel() const;

    UFUNCTION(BlueprintCallable, Category = "Experience")
    float GetExperiencePercent() const;

    UFUNCTION(BlueprintCallable, Category = "Experience")
    bool IsLevelUpPending() const { return bLevelUpPending; }

    // Upgrade system
    UFUNCTION(BlueprintCallable, Category = "Upgrades")
    TArray<FAbilityUpgradeOption> GetAvailableUpgrades() const;

    UFUNCTION(BlueprintCallable, Category = "Upgrades")
    void GenerateUpgradeOptions();

protected:
    virtual void BeginPlay() override;

    // Level up processing
    UFUNCTION(BlueprintCallable, Category = "Experience")
    void ProcessLevelUp();

    UFUNCTION(BlueprintCallable, Category = "Experience")
    void ApplyUpgrade(const FAbilityUpgradeOption& Upgrade);

    // Upgrade generation
    UFUNCTION(BlueprintCallable, Category = "Upgrades")
    TArray<FAbilityUpgradeOption> FilterUpgradesByRequirements(const TArray<FAbilityUpgradeOption>& AllUpgrades) const;

    UFUNCTION(BlueprintCallable, Category = "Upgrades")
    bool MeetsUpgradeRequirements(const FAbilityUpgradeOption& Upgrade) const;

    // Available upgrade options
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Upgrades")
    TArray<FAbilityUpgradeOption> AllUpgradeOptions;

    // Upgrade selection settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Upgrades")
    int32 UpgradeChoicesPerLevel = 3;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Upgrades")
    bool bPauseGameOnLevelUp = true;

    // Experience settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Experience")
    float BaseExperienceToNextLevel = 100.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Experience")
    float ExperienceScalingFactor = 1.2f;

private:
    // Current state
    bool bLevelUpPending = false;
    TArray<FAbilityUpgradeOption> CurrentUpgradeOptions;

    // Cached player reference
    UPROPERTY()
    TObjectPtr<ASurvivorsPlayerCharacter> PlayerCharacter;

    // Get player character
    ASurvivorsPlayerCharacter* GetPlayerCharacter();

    // Pause/unpause game
    void SetGamePaused(bool bPaused);
};

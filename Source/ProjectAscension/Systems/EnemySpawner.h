#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "EnemySpawner.generated.h"

class ASurvivorsEnemyCharacter;
class ASurvivorsPlayerCharacter;

USTRUCT(BlueprintType)
struct FEnemySpawnData
{
    GENERATED_BODY()

    // Enemy class to spawn
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn")
    TSubclassOf<ASurvivorsEnemyCharacter> EnemyClass;

    // Spawn weight (higher = more likely to spawn)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn")
    float SpawnWeight = 1.0f;

    // Level requirements
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn")
    int32 MinPlayerLevel = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn")
    int32 MaxPlayerLevel = 999;

    // Wave requirements
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn")
    int32 MinWave = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn")
    int32 MaxWave = 999;

    // Spawn limits
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn")
    int32 MaxConcurrentSpawns = -1; // -1 = no limit

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn")
    float SpawnCooldown = 0.0f;

    // Tags for conditional spawning
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn")
    FGameplayTagContainer RequiredTags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn")
    FGameplayTagContainer BlockedTags;

    FEnemySpawnData()
    {
        EnemyClass = nullptr;
        SpawnWeight = 1.0f;
        MinPlayerLevel = 1;
        MaxPlayerLevel = 999;
        MinWave = 1;
        MaxWave = 999;
        MaxConcurrentSpawns = -1;
        SpawnCooldown = 0.0f;
    }
};

USTRUCT(BlueprintType)
struct FSpawnWaveData
{
    GENERATED_BODY()

    // Wave number
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave")
    int32 WaveNumber = 1;

    // Number of enemies to spawn in this wave
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave")
    int32 EnemyCount = 5;

    // Time between enemy spawns in this wave
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave")
    float SpawnInterval = 1.0f;

    // Difficulty multiplier for this wave
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave")
    float DifficultyMultiplier = 1.0f;

    // Special spawn data for this wave (overrides default)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wave")
    TArray<FEnemySpawnData> SpecialSpawns;

    FSpawnWaveData()
    {
        WaveNumber = 1;
        EnemyCount = 5;
        SpawnInterval = 1.0f;
        DifficultyMultiplier = 1.0f;
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWaveStarted, int32, WaveNumber);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWaveCompleted, int32, WaveNumber);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEnemySpawned, ASurvivorsEnemyCharacter*, SpawnedEnemy, int32, WaveNumber);

/**
 * UEnemySpawner
 * 
 * Advanced enemy spawning system for the Survivors game.
 * Handles wave-based spawning, enemy variety, difficulty scaling, and spawn positioning.
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class PROJECTASCENSION_API UEnemySpawner : public UActorComponent
{
    GENERATED_BODY()

public:
    UEnemySpawner();

    // Delegates
    UPROPERTY(BlueprintAssignable, Category = "Spawner")
    FOnWaveStarted OnWaveStarted;

    UPROPERTY(BlueprintAssignable, Category = "Spawner")
    FOnWaveCompleted OnWaveCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Spawner")
    FOnEnemySpawned OnEnemySpawned;

    // Spawning control
    UFUNCTION(BlueprintCallable, Category = "Spawner")
    void StartSpawning();

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    void StopSpawning();

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    void PauseSpawning();

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    void ResumeSpawning();

    // Wave management
    UFUNCTION(BlueprintCallable, Category = "Spawner")
    void StartNextWave();

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    void ForceCompleteCurrentWave();

    // Manual spawning
    UFUNCTION(BlueprintCallable, Category = "Spawner")
    ASurvivorsEnemyCharacter* SpawnEnemy(TSubclassOf<ASurvivorsEnemyCharacter> EnemyClass, const FVector& SpawnLocation);

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    TArray<ASurvivorsEnemyCharacter*> SpawnEnemyGroup(const TArray<TSubclassOf<ASurvivorsEnemyCharacter>>& EnemyClasses, const FVector& CenterLocation, float Radius = 200.0f);

    // Getters
    UFUNCTION(BlueprintCallable, Category = "Spawner")
    int32 GetCurrentWave() const { return CurrentWave; }

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    int32 GetActiveEnemyCount() const;

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    bool IsSpawning() const { return bIsSpawning && !bIsPaused; }

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    float GetCurrentDifficultyMultiplier() const { return CurrentDifficultyMultiplier; }

protected:
    virtual void BeginPlay() override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Spawning logic
    UFUNCTION(BlueprintCallable, Category = "Spawner")
    void UpdateSpawning(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    TSubclassOf<ASurvivorsEnemyCharacter> SelectEnemyToSpawn() const;

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    FVector GetSpawnLocation() const;

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    bool CanSpawnEnemy(const FEnemySpawnData& SpawnData) const;

    // Wave management
    UFUNCTION(BlueprintCallable, Category = "Spawner")
    void ProcessWaveCompletion();

    UFUNCTION(BlueprintCallable, Category = "Spawner")
    FSpawnWaveData GetCurrentWaveData() const;

    // Spawn configuration
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Spawner|Configuration")
    TArray<FEnemySpawnData> DefaultSpawnData;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Spawner|Configuration")
    TArray<FSpawnWaveData> WaveConfiguration;

    // Spawn settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Spawner|Settings")
    float BaseSpawnInterval = 2.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Spawner|Settings")
    int32 BaseEnemiesPerWave = 5;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Spawner|Settings")
    float SpawnRadius = 1500.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Spawner|Settings")
    float MinSpawnDistance = 800.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Spawner|Settings")
    int32 MaxConcurrentEnemies = 50;

    // Difficulty scaling
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Spawner|Difficulty")
    float DifficultyIncreaseInterval = 30.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Spawner|Difficulty")
    float DifficultyMultiplierPerWave = 1.1f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Spawner|Difficulty")
    float MaxDifficultyMultiplier = 5.0f;

private:
    // Spawning state
    bool bIsSpawning = false;
    bool bIsPaused = false;
    float SpawnTimer = 0.0f;
    float DifficultyTimer = 0.0f;

    // Wave state
    int32 CurrentWave = 1;
    int32 EnemiesSpawnedThisWave = 0;
    int32 EnemiesRemainingThisWave = 0;
    float CurrentDifficultyMultiplier = 1.0f;

    // Tracking
    TArray<TWeakObjectPtr<ASurvivorsEnemyCharacter>> SpawnedEnemies;
    TMap<TSubclassOf<ASurvivorsEnemyCharacter>, float> LastSpawnTimes;

    // Cached references
    UPROPERTY()
    TObjectPtr<ASurvivorsPlayerCharacter> PlayerCharacter;

    // Helper functions
    ASurvivorsPlayerCharacter* GetPlayerCharacter();
    void CleanupDeadEnemies();
    void UpdateDifficulty(float DeltaTime);
};

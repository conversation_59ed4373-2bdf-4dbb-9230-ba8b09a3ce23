#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "SurvivorsAudioManager.generated.h"

class USoundBase;
class UAudioComponent;

UENUM(BlueprintType)
enum class ESurvivorsAudioType : uint8
{
    SFX,
    Music,
    UI,
    Voice
};

USTRUCT(BlueprintType)
struct FSurvivorsAudioData : public FTableRowBase
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    FName AudioID;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    TSoftObjectPtr<USoundBase> Sound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    ESurvivorsAudioType AudioType = ESurvivorsAudioType::SFX;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    float Volume = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    float Pitch = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    bool bLooping = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    bool b3D = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    float MaxDistance = 1000.0f;

    FSurvivorsAudioData()
    {
        AudioID = NAME_None;
        Sound = nullptr;
        AudioType = ESurvivorsAudioType::SFX;
        Volume = 1.0f;
        Pitch = 1.0f;
        bLooping = false;
        b3D = true;
        MaxDistance = 1000.0f;
    }
};

/**
 * USurvivorsAudioManager
 * 
 * Centralized audio management system for the Survivors game.
 * Handles sound effects, music, and audio settings.
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class PROJECTASCENSION_API USurvivorsAudioManager : public UActorComponent
{
    GENERATED_BODY()

public:
    USurvivorsAudioManager();

    // Play audio by ID
    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    UAudioComponent* PlayAudio(FName AudioID, const FVector& Location = FVector::ZeroVector, AActor* AttachToActor = nullptr);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    UAudioComponent* PlayAudio2D(FName AudioID);

    // Stop audio
    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    void StopAudio(FName AudioID);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    void StopAllAudio();

    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    void StopAudioByType(ESurvivorsAudioType AudioType);

    // Music management
    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    void PlayMusic(FName MusicID, bool bFadeIn = true, float FadeTime = 2.0f);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    void StopMusic(bool bFadeOut = true, float FadeTime = 2.0f);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    void SetMusicVolume(float Volume);

    // Volume settings
    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    void SetMasterVolume(float Volume);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    void SetSFXVolume(float Volume);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    void SetUIVolume(float Volume);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    float GetMasterVolume() const { return MasterVolume; }

    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    float GetSFXVolume() const { return SFXVolume; }

    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    float GetMusicVolume() const { return MusicVolume; }

    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    float GetUIVolume() const { return UIVolume; }

    // Audio data management
    UFUNCTION(BlueprintCallable, Category = "Survivors|Audio")
    bool GetAudioData(FName AudioID, FSurvivorsAudioData& OutAudioData);

protected:
    virtual void BeginPlay() override;

    // Audio data table
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Audio")
    TObjectPtr<UDataTable> AudioDataTable;

    // Volume settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Audio")
    float MasterVolume = 1.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Audio")
    float SFXVolume = 1.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Audio")
    float MusicVolume = 0.7f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Audio")
    float UIVolume = 1.0f;

    // Current music
    UPROPERTY(BlueprintReadOnly, Category = "Survivors|Audio")
    TObjectPtr<UAudioComponent> CurrentMusicComponent;

    UPROPERTY(BlueprintReadOnly, Category = "Survivors|Audio")
    FName CurrentMusicID;

private:
    // Active audio components
    UPROPERTY()
    TMap<FName, TObjectPtr<UAudioComponent>> ActiveAudioComponents;

    // Calculate final volume for audio type
    float CalculateFinalVolume(ESurvivorsAudioType AudioType, float BaseVolume) const;

    // Cleanup finished audio components
    void CleanupFinishedAudio();

    // Load audio data from table
    void LoadAudioData();

    // Cached audio data for performance
    UPROPERTY()
    TMap<FName, FSurvivorsAudioData> CachedAudioData;
};

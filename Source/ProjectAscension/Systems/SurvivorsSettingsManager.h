#pragma once

#include "CoreMinimal.h"
#include "Engine/GameInstanceSubsystem.h"
#include "SurvivorsSettingsManager.generated.h"

USTRUCT(BlueprintType)
struct FSurvivorsGameplaySettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay")
    bool bShowDamageNumbers = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay")
    bool bAutoPickupItems = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay")
    bool bPauseOnLevelUp = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay")
    float CameraDistance = 800.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay")
    float UIScale = 1.0f;

    FSurvivorsGameplaySettings()
    {
        bShowDamageNumbers = true;
        bAutoPickupItems = false;
        bPauseOnLevelUp = true;
        CameraDistance = 800.0f;
        UIScale = 1.0f;
    }
};

USTRUCT(BlueprintType)
struct FSurvivorsAudioSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MasterVolume = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float SFXVolume = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MusicVolume = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float UIVolume = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    bool bMuteWhenUnfocused = true;

    FSurvivorsAudioSettings()
    {
        MasterVolume = 1.0f;
        SFXVolume = 1.0f;
        MusicVolume = 0.7f;
        UIVolume = 1.0f;
        bMuteWhenUnfocused = true;
    }
};

USTRUCT(BlueprintType)
struct FSurvivorsVideoSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Video")
    int32 ResolutionX = 1920;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Video")
    int32 ResolutionY = 1080;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Video")
    bool bFullscreen = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Video")
    bool bVSync = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Video", meta = (ClampMin = "30", ClampMax = "144"))
    int32 FrameRateLimit = 60;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Video", meta = (ClampMin = "0", ClampMax = "4"))
    int32 GraphicsQuality = 2; // 0=Low, 1=Medium, 2=High, 3=Epic, 4=Cinematic

    FSurvivorsVideoSettings()
    {
        ResolutionX = 1920;
        ResolutionY = 1080;
        bFullscreen = false;
        bVSync = true;
        FrameRateLimit = 60;
        GraphicsQuality = 2;
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSettingsChanged, const FString&, SettingCategory);

/**
 * USurvivorsSettingsManager
 * 
 * Manages game settings including audio, video, and gameplay preferences.
 * Handles saving/loading settings and applying changes.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API USurvivorsSettingsManager : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    USurvivorsSettingsManager();

    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // Delegates
    UPROPERTY(BlueprintAssignable, Category = "Survivors|Settings")
    FOnSettingsChanged OnSettingsChanged;

    // Settings access
    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    const FSurvivorsGameplaySettings& GetGameplaySettings() const { return GameplaySettings; }

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    const FSurvivorsAudioSettings& GetAudioSettings() const { return AudioSettings; }

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    const FSurvivorsVideoSettings& GetVideoSettings() const { return VideoSettings; }

    // Settings modification
    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void SetGameplaySettings(const FSurvivorsGameplaySettings& NewSettings);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void SetAudioSettings(const FSurvivorsAudioSettings& NewSettings);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void SetVideoSettings(const FSurvivorsVideoSettings& NewSettings);

    // Individual setting setters
    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void SetMasterVolume(float Volume);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void SetSFXVolume(float Volume);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void SetMusicVolume(float Volume);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void SetUIVolume(float Volume);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void SetShowDamageNumbers(bool bShow);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void SetCameraDistance(float Distance);

    // Save/Load
    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void SaveSettings();

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void LoadSettings();

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void ResetToDefaults();

    // Apply settings
    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void ApplyAudioSettings();

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void ApplyVideoSettings();

    UFUNCTION(BlueprintCallable, Category = "Survivors|Settings")
    void ApplyGameplaySettings();

protected:
    // Settings data
    UPROPERTY(BlueprintReadOnly, Category = "Survivors|Settings")
    FSurvivorsGameplaySettings GameplaySettings;

    UPROPERTY(BlueprintReadOnly, Category = "Survivors|Settings")
    FSurvivorsAudioSettings AudioSettings;

    UPROPERTY(BlueprintReadOnly, Category = "Survivors|Settings")
    FSurvivorsVideoSettings VideoSettings;

    // Settings file
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Settings")
    FString SettingsFileName = TEXT("SurvivorsSettings");

private:
    // Apply individual setting categories
    void ApplyAudioSettingsInternal();
    void ApplyVideoSettingsInternal();
    void ApplyGameplaySettingsInternal();

    // Get settings file path
    FString GetSettingsFilePath() const;

    // Validate settings
    void ValidateSettings();
};

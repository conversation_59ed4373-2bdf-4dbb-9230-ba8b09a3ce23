#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "DamageNumberManager.generated.h"

class USurvivorsDamageNumberWidget;

/**
 * UDamageNumberManager
 * 
 * Manages the spawning and display of floating damage numbers.
 * Handles world-to-screen conversion and widget lifecycle.
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class PROJECTASCENSION_API UDamageNumberManager : public UActorComponent
{
    GENERATED_BODY()

public:
    UDamageNumberManager();

    // Spawn damage number at world location
    UFUNCTION(BlueprintCallable, Category = "Survivors|DamageNumbers")
    void SpawnDamageNumber(float DamageAmount, const FVector& WorldLocation, bool bIsCritical = false, bool bIsHealing = false);

    // Spawn damage number at actor location
    UFUNCTION(BlueprintCallable, Category = "Survivors|DamageNumbers")
    void SpawnDamageNumberAtActor(float DamageAmount, AActor* TargetActor, bool bIsCritical = false, bool bIsHealing = false);

    // Enable/disable damage numbers
    UFUNCTION(BlueprintCallable, Category = "Survivors|DamageNumbers")
    void SetDamageNumbersEnabled(bool bEnabled) { bDamageNumbersEnabled = bEnabled; }

    UFUNCTION(BlueprintCallable, Category = "Survivors|DamageNumbers")
    bool AreDamageNumbersEnabled() const { return bDamageNumbersEnabled; }

protected:
    virtual void BeginPlay() override;

    // Create damage number widget
    UFUNCTION(BlueprintCallable, Category = "Survivors|DamageNumbers")
    USurvivorsDamageNumberWidget* CreateDamageNumberWidget(float DamageAmount, bool bIsCritical, bool bIsHealing);

    // Position widget at screen location
    UFUNCTION(BlueprintCallable, Category = "Survivors|DamageNumbers")
    void PositionWidgetAtScreenLocation(UUserWidget* Widget, const FVector2D& ScreenLocation);

    // Convert world location to screen position
    UFUNCTION(BlueprintCallable, Category = "Survivors|DamageNumbers")
    bool WorldToScreenPosition(const FVector& WorldLocation, FVector2D& ScreenPosition);

    // Widget settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumbers")
    TSubclassOf<USurvivorsDamageNumberWidget> DamageNumberWidgetClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumbers")
    bool bDamageNumbersEnabled = true;

    // Positioning settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumbers")
    FVector2D RandomOffset = FVector2D(50.0f, 30.0f);

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumbers")
    float VerticalOffset = 50.0f;

    // Performance settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumbers")
    int32 MaxConcurrentDamageNumbers = 20;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|DamageNumbers")
    float MinDamageToShow = 1.0f;

private:
    // Track active damage number widgets
    UPROPERTY()
    TArray<TObjectPtr<USurvivorsDamageNumberWidget>> ActiveDamageNumbers;

    // Cleanup old damage numbers
    void CleanupOldDamageNumbers();

    // Get player controller
    APlayerController* GetPlayerController() const;

    // Add random offset to position
    FVector2D AddRandomOffset(const FVector2D& BasePosition) const;
};

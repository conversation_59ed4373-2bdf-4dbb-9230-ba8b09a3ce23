#include "DamageNumberManager.h"
#include "../UI/SurvivorsDamageNumberWidget.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"
#include "Blueprint/UserWidget.h"
#include "Blueprint/WidgetLayoutLibrary.h"

UDamageNumberManager::UDamageNumberManager()
{
    PrimaryComponentTick.bCanEverTick = false;

    bDamageNumbersEnabled = true;
    RandomOffset = FVector2D(50.0f, 30.0f);
    VerticalOffset = 50.0f;
    MaxConcurrentDamageNumbers = 20;
    MinDamageToShow = 1.0f;
}

void UDamageNumberManager::BeginPlay()
{
    Super::BeginPlay();
}

void UDamageNumberManager::SpawnDamageNumber(float DamageAmount, const FVector& WorldLocation, bool bIsCritical, bool bIsHealing)
{
    if (!bDamageNumbersEnabled || DamageAmount < MinDamageToShow || !DamageNumberWidgetClass)
    {
        return;
    }

    // Convert world location to screen position
    FVector2D ScreenPosition;
    if (!WorldToScreenPosition(WorldLocation, ScreenPosition))
    {
        return; // Location is not visible on screen
    }

    // Add vertical offset and random offset
    ScreenPosition.Y -= VerticalOffset;
    ScreenPosition = AddRandomOffset(ScreenPosition);

    // Create damage number widget
    USurvivorsDamageNumberWidget* DamageWidget = CreateDamageNumberWidget(DamageAmount, bIsCritical, bIsHealing);
    if (DamageWidget)
    {
        // Position the widget
        PositionWidgetAtScreenLocation(DamageWidget, ScreenPosition);

        // Start animation
        DamageWidget->StartAnimation();

        // Track the widget
        ActiveDamageNumbers.Add(DamageWidget);

        // Cleanup old damage numbers if we have too many
        CleanupOldDamageNumbers();
    }
}

void UDamageNumberManager::SpawnDamageNumberAtActor(float DamageAmount, AActor* TargetActor, bool bIsCritical, bool bIsHealing)
{
    if (!TargetActor)
    {
        return;
    }

    // Get actor location with some height offset
    FVector WorldLocation = TargetActor->GetActorLocation();
    WorldLocation.Z += 100.0f; // Offset above the actor

    SpawnDamageNumber(DamageAmount, WorldLocation, bIsCritical, bIsHealing);
}

USurvivorsDamageNumberWidget* UDamageNumberManager::CreateDamageNumberWidget(float DamageAmount, bool bIsCritical, bool bIsHealing)
{
    if (!DamageNumberWidgetClass)
    {
        return nullptr;
    }

    USurvivorsDamageNumberWidget* DamageWidget = CreateWidget<USurvivorsDamageNumberWidget>(GetWorld(), DamageNumberWidgetClass);
    if (DamageWidget)
    {
        DamageWidget->SetDamageNumber(DamageAmount, bIsCritical, bIsHealing);
        DamageWidget->AddToViewport(100); // High Z-order for damage numbers
    }

    return DamageWidget;
}

void UDamageNumberManager::PositionWidgetAtScreenLocation(UUserWidget* Widget, const FVector2D& ScreenLocation)
{
    if (!Widget)
    {
        return;
    }

    // Set widget position
    Widget->SetPositionInViewport(ScreenLocation, false);
}

bool UDamageNumberManager::WorldToScreenPosition(const FVector& WorldLocation, FVector2D& ScreenPosition)
{
    APlayerController* PC = GetPlayerController();
    if (!PC)
    {
        return false;
    }

    // Project world location to screen
    return PC->ProjectWorldLocationToScreen(WorldLocation, ScreenPosition, true);
}

void UDamageNumberManager::CleanupOldDamageNumbers()
{
    // Remove null or invalid widgets
    ActiveDamageNumbers.RemoveAll([](const TObjectPtr<USurvivorsDamageNumberWidget>& Widget)
    {
        return !IsValid(Widget) || !Widget->IsInViewport();
    });

    // If we still have too many, remove the oldest ones
    while (ActiveDamageNumbers.Num() > MaxConcurrentDamageNumbers)
    {
        if (USurvivorsDamageNumberWidget* OldestWidget = ActiveDamageNumbers[0])
        {
            OldestWidget->RemoveFromParent();
        }
        ActiveDamageNumbers.RemoveAt(0);
    }
}

APlayerController* UDamageNumberManager::GetPlayerController() const
{
    return UGameplayStatics::GetPlayerController(GetWorld(), 0);
}

FVector2D UDamageNumberManager::AddRandomOffset(const FVector2D& BasePosition) const
{
    FVector2D Offset;
    Offset.X = FMath::RandRange(-RandomOffset.X, RandomOffset.X);
    Offset.Y = FMath::RandRange(-RandomOffset.Y, RandomOffset.Y);
    
    return BasePosition + Offset;
}

#include "ExperienceManager.h"
#include "../Characters/SurvivorsPlayerCharacter.h"
#include "../GAS/SurvivorsAbilitySystemComponent.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "GameplayAbility.h"
#include "GameplayEffect.h"

UExperienceManager::UExperienceManager()
{
    PrimaryComponentTick.bCanEverTick = false;

    // Set default values
    UpgradeChoicesPerLevel = 3;
    bPauseGameOnLevelUp = true;
    BaseExperienceToNextLevel = 100.0f;
    ExperienceScalingFactor = 1.2f;
}

void UExperienceManager::BeginPlay()
{
    Super::BeginPlay();

    // Cache player character
    PlayerCharacter = GetPlayerCharacter();

    // Bind to level up events if we have a player character
    if (PlayerCharacter && PlayerCharacter->GetAbilitySystemComponent())
    {
        USurvivorsAbilitySystemComponent* ASC = Cast<USurvivorsAbilitySystemComponent>(PlayerCharacter->GetAbilitySystemComponent());
        if (ASC && ASC->GetSurvivorsAttributeSet())
        {
            // Bind to level attribute changes
            ASC->GetGameplayAttributeValueChangeDelegate(ASC->GetSurvivorsAttributeSet()->GetLevelAttribute())
                .AddUObject(this, &UExperienceManager::TriggerLevelUp);

            // Bind to experience attribute changes
            ASC->GetGameplayAttributeValueChangeDelegate(ASC->GetSurvivorsAttributeSet()->GetExperienceAttribute())
                .AddLambda([this](const FOnAttributeChangeData& Data) {
                    OnExperienceGained.Broadcast(Data.NewValue - Data.OldValue, Data.NewValue);
                });
        }
    }
}

void UExperienceManager::AddExperience(float Amount)
{
    if (!PlayerCharacter)
    {
        return;
    }

    USurvivorsAbilitySystemComponent* ASC = Cast<USurvivorsAbilitySystemComponent>(PlayerCharacter->GetAbilitySystemComponent());
    if (!ASC)
    {
        return;
    }

    // Apply experience through GAS
    // This will trigger level-up automatically through the attribute set
    if (USurvivorsAttributeSet* AttributeSet = ASC->GetSurvivorsAttributeSet())
    {
        const float CurrentExp = AttributeSet->GetExperience();
        ASC->ApplyModToAttributeUnsafe(AttributeSet->GetExperienceAttribute(), EGameplayModOp::Override, CurrentExp + Amount);
    }
}

void UExperienceManager::TriggerLevelUp()
{
    if (bLevelUpPending)
    {
        return; // Already processing a level up
    }

    bLevelUpPending = true;
    ProcessLevelUp();
}

void UExperienceManager::ProcessLevelUp()
{
    if (!PlayerCharacter)
    {
        bLevelUpPending = false;
        return;
    }

    const int32 NewLevel = GetCurrentLevel();
    
    // Broadcast level up event
    OnLevelUp.Broadcast(NewLevel);

    // Generate upgrade options
    GenerateUpgradeOptions();

    // Pause game if configured
    if (bPauseGameOnLevelUp)
    {
        SetGamePaused(true);
    }

    // Broadcast upgrade options
    OnUpgradeOptionsAvailable.Broadcast(CurrentUpgradeOptions);

    UE_LOG(LogTemp, Log, TEXT("Player leveled up to level %d!"), NewLevel);
}

void UExperienceManager::GenerateUpgradeOptions()
{
    CurrentUpgradeOptions.Empty();

    // Get all available upgrades that meet requirements
    TArray<FAbilityUpgradeOption> AvailableUpgrades = FilterUpgradesByRequirements(AllUpgradeOptions);

    // Randomly select upgrade choices
    const int32 NumChoices = FMath::Min(UpgradeChoicesPerLevel, AvailableUpgrades.Num());
    
    for (int32 i = 0; i < NumChoices; i++)
    {
        if (AvailableUpgrades.Num() > 0)
        {
            const int32 RandomIndex = FMath::RandRange(0, AvailableUpgrades.Num() - 1);
            CurrentUpgradeOptions.Add(AvailableUpgrades[RandomIndex]);
            AvailableUpgrades.RemoveAt(RandomIndex);
        }
    }
}

void UExperienceManager::SelectUpgrade(int32 UpgradeIndex)
{
    if (!CurrentUpgradeOptions.IsValidIndex(UpgradeIndex))
    {
        UE_LOG(LogTemp, Warning, TEXT("Invalid upgrade index: %d"), UpgradeIndex);
        return;
    }

    const FAbilityUpgradeOption& SelectedUpgrade = CurrentUpgradeOptions[UpgradeIndex];
    ApplyUpgrade(SelectedUpgrade);

    // Clear pending state
    bLevelUpPending = false;
    CurrentUpgradeOptions.Empty();

    // Unpause game
    if (bPauseGameOnLevelUp)
    {
        SetGamePaused(false);
    }

    UE_LOG(LogTemp, Log, TEXT("Applied upgrade: %s"), *SelectedUpgrade.DisplayName.ToString());
}

void UExperienceManager::ApplyUpgrade(const FAbilityUpgradeOption& Upgrade)
{
    if (!PlayerCharacter)
    {
        return;
    }

    USurvivorsAbilitySystemComponent* ASC = Cast<USurvivorsAbilitySystemComponent>(PlayerCharacter->GetAbilitySystemComponent());
    if (!ASC)
    {
        return;
    }

    // Apply ability upgrade
    if (Upgrade.AbilityClass)
    {
        // Check if player already has this ability
        bool bHasAbility = false;
        for (const FGameplayAbilitySpec& Spec : ASC->GetActivatableAbilities())
        {
            if (Spec.Ability && Spec.Ability->IsA(Upgrade.AbilityClass))
            {
                // Upgrade existing ability level
                ASC->SetAbilityLevel(Spec.Handle, Spec.Level + 1);
                bHasAbility = true;
                break;
            }
        }

        // Grant new ability if not already owned
        if (!bHasAbility)
        {
            ASC->GiveAbility(FGameplayAbilitySpec(Upgrade.AbilityClass, 1, INDEX_NONE, PlayerCharacter));
        }
    }

    // Apply stat boost effect
    if (Upgrade.StatBoostEffect)
    {
        FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
        EffectContext.AddSourceObject(PlayerCharacter);

        FGameplayEffectSpecHandle SpecHandle = ASC->MakeOutgoingSpec(Upgrade.StatBoostEffect, GetCurrentLevel(), EffectContext);
        if (SpecHandle.IsValid())
        {
            ASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        }
    }
}

TArray<FAbilityUpgradeOption> UExperienceManager::FilterUpgradesByRequirements(const TArray<FAbilityUpgradeOption>& AllUpgrades) const
{
    TArray<FAbilityUpgradeOption> FilteredUpgrades;

    for (const FAbilityUpgradeOption& Upgrade : AllUpgrades)
    {
        if (MeetsUpgradeRequirements(Upgrade))
        {
            FilteredUpgrades.Add(Upgrade);
        }
    }

    return FilteredUpgrades;
}

bool UExperienceManager::MeetsUpgradeRequirements(const FAbilityUpgradeOption& Upgrade) const
{
    // Check level requirement
    if (GetCurrentLevel() < Upgrade.RequiredLevel)
    {
        return false;
    }

    // Check tag requirements
    if (!Upgrade.RequiredTags.IsEmpty() && PlayerCharacter)
    {
        USurvivorsAbilitySystemComponent* ASC = Cast<USurvivorsAbilitySystemComponent>(PlayerCharacter->GetAbilitySystemComponent());
        if (ASC && !ASC->HasAllMatchingGameplayTags(Upgrade.RequiredTags))
        {
            return false;
        }
    }

    return true;
}

TArray<FAbilityUpgradeOption> UExperienceManager::GetAvailableUpgrades() const
{
    return CurrentUpgradeOptions;
}

// Getters
int32 UExperienceManager::GetCurrentLevel() const
{
    if (PlayerCharacter)
    {
        return PlayerCharacter->GetCharacterLevel();
    }
    return 1;
}

float UExperienceManager::GetCurrentExperience() const
{
    if (PlayerCharacter)
    {
        return PlayerCharacter->GetExperience();
    }
    return 0.0f;
}

float UExperienceManager::GetExperienceToNextLevel() const
{
    if (PlayerCharacter)
    {
        USurvivorsAbilitySystemComponent* ASC = Cast<USurvivorsAbilitySystemComponent>(PlayerCharacter->GetAbilitySystemComponent());
        if (ASC)
        {
            return ASC->GetExperienceToNextLevel();
        }
    }
    return BaseExperienceToNextLevel;
}

float UExperienceManager::GetExperiencePercent() const
{
    if (PlayerCharacter)
    {
        USurvivorsAbilitySystemComponent* ASC = Cast<USurvivorsAbilitySystemComponent>(PlayerCharacter->GetAbilitySystemComponent());
        if (ASC)
        {
            return ASC->GetExperiencePercent();
        }
    }
    return 0.0f;
}

ASurvivorsPlayerCharacter* UExperienceManager::GetPlayerCharacter()
{
    if (!PlayerCharacter)
    {
        if (APawn* Pawn = UGameplayStatics::GetPlayerPawn(GetWorld(), 0))
        {
            PlayerCharacter = Cast<ASurvivorsPlayerCharacter>(Pawn);
        }
    }
    return PlayerCharacter;
}

void UExperienceManager::SetGamePaused(bool bPaused)
{
    if (APlayerController* PC = UGameplayStatics::GetPlayerController(GetWorld(), 0))
    {
        UGameplayStatics::SetGamePaused(GetWorld(), bPaused);
    }
}

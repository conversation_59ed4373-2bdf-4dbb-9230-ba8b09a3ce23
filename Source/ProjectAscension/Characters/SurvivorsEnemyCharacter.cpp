#include "SurvivorsEnemyCharacter.h"
#include "SurvivorsPlayerCharacter.h"
#include "../GAS/SurvivorsAbilitySystemComponent.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "../GameplayEffects/GE_ExperienceGain.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/CapsuleComponent.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "AIController.h"

ASurvivorsEnemyCharacter::ASurvivorsEnemyCharacter()
{
    PrimaryActorTick.bCanEverTick = true;

    // Configure for AI
    AIControllerClass = AAIController::StaticClass();

    // Set default enemy type
    EnemyType = FSurvivorsGameplayTags::Get().Enemy_Type_Basic;

    // Configure movement for enemy
    GetCharacterMovement()->MaxWalkSpeed = MovementSpeed;
    GetCharacterMovement()->bOrientRotationToMovement = true;
    GetCharacterMovement()->RotationRate = FRotator(0.0f, 540.0f, 0.0f);

    // Set default collision
    GetCapsuleComponent()->SetCollisionResponseToChannel(ECollisionChannel::ECC_Camera, ECollisionResponse::ECR_Ignore);

    // Set default experience effect
    ExperienceEffect = UGE_ExperienceGain::StaticClass();
}

void ASurvivorsEnemyCharacter::BeginPlay()
{
    Super::BeginPlay();

    // Initialize AI update timer
    AIUpdateTimer = FMath::RandRange(0.0f, AIUpdateInterval);

    // Set movement speed from attribute
    if (AttributeSet)
    {
        GetCharacterMovement()->MaxWalkSpeed = GetMovementSpeed();
    }
}

void ASurvivorsEnemyCharacter::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Update AI at intervals for performance
    AIUpdateTimer += DeltaTime;
    if (AIUpdateTimer >= AIUpdateInterval)
    {
        UpdateAI(DeltaTime);
        AIUpdateTimer = 0.0f;
    }
}

void ASurvivorsEnemyCharacter::PossessedBy(AController* NewController)
{
    Super::PossessedBy(NewController);

    // Give AI-specific abilities
    if (AbilitySystemComponent && GetLocalRole() == ROLE_Authority)
    {
        if (PursuitAbilityClass)
        {
            AbilitySystemComponent->GiveAbility(FGameplayAbilitySpec(PursuitAbilityClass, GetCharacterLevel(), INDEX_NONE, this));
        }

        if (EnemyAttackAbilityClass)
        {
            AbilitySystemComponent->GiveAbility(FGameplayAbilitySpec(EnemyAttackAbilityClass, GetCharacterLevel(), INDEX_NONE, this));
        }
    }
}

void ASurvivorsEnemyCharacter::UpdateAI(float DeltaTime)
{
    if (!IsAlive())
    {
        return;
    }

    // Find target player
    ASurvivorsPlayerCharacter* NearestPlayer = FindNearestPlayer();
    
    if (NearestPlayer && IsPlayerInSightRange())
    {
        TargetPlayer = NearestPlayer;
        bHasTarget = true;
        LastKnownPlayerLocation = NearestPlayer->GetActorLocation();

        // Check if we can attack
        if (IsPlayerInAttackRange())
        {
            AttackTarget();
        }
        else
        {
            MoveTowardsTarget();
        }
    }
    else
    {
        // Lost target or no target found
        if (bHasTarget && !LastKnownPlayerLocation.IsZero())
        {
            // Move to last known location
            MoveTowardsTarget();
            
            // Check if we reached the last known location
            const float DistanceToLastKnown = FVector::Dist(GetActorLocation(), LastKnownPlayerLocation);
            if (DistanceToLastKnown < 100.0f)
            {
                bHasTarget = false;
                LastKnownPlayerLocation = FVector::ZeroVector;
            }
        }
        else
        {
            bHasTarget = false;
            TargetPlayer = nullptr;
        }
    }
}

void ASurvivorsEnemyCharacter::MoveTowardsTarget()
{
    if (!bHasTarget)
    {
        return;
    }

    FVector TargetLocation = TargetPlayer.IsValid() ? TargetPlayer->GetActorLocation() : LastKnownPlayerLocation;
    
    if (!TargetLocation.IsZero())
    {
        // Calculate direction to target
        FVector Direction = (TargetLocation - GetActorLocation()).GetSafeNormal();
        
        // Add movement input
        AddMovementInput(Direction, 1.0f);
        
        // Try to activate movement ability
        if (AbilitySystemComponent)
        {
            AbilitySystemComponent->TryActivateAbilityByTag(FSurvivorsGameplayTags::Get().Ability_Movement);
        }

        bIsMovingToTarget = true;
    }
}

void ASurvivorsEnemyCharacter::AttackTarget()
{
    if (!TargetPlayer.IsValid() || !IsPlayerInAttackRange())
    {
        return;
    }

    // Check attack cooldown
    const float CurrentTime = GetWorld()->GetTimeSeconds();
    if (CurrentTime - LastAttackTime < AttackCooldown)
    {
        return;
    }

    // Try to activate attack ability
    if (AbilitySystemComponent)
    {
        if (AbilitySystemComponent->TryActivateAbilityByTag(FSurvivorsGameplayTags::Get().Ability_AutoAttack))
        {
            LastAttackTime = CurrentTime;
        }
    }
}

ASurvivorsPlayerCharacter* ASurvivorsEnemyCharacter::FindNearestPlayer() const
{
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ASurvivorsPlayerCharacter::StaticClass(), FoundActors);

    ASurvivorsPlayerCharacter* NearestPlayer = nullptr;
    float NearestDistance = FLT_MAX;
    const FVector EnemyLocation = GetActorLocation();

    for (AActor* Actor : FoundActors)
    {
        if (ASurvivorsPlayerCharacter* Player = Cast<ASurvivorsPlayerCharacter>(Actor))
        {
            if (Player->IsAlive())
            {
                const float Distance = FVector::Dist(EnemyLocation, Player->GetActorLocation());
                if (Distance < NearestDistance)
                {
                    NearestDistance = Distance;
                    NearestPlayer = Player;
                }
            }
        }
    }

    return NearestPlayer;
}

bool ASurvivorsEnemyCharacter::IsPlayerInAttackRange() const
{
    if (!TargetPlayer.IsValid())
    {
        return false;
    }

    const float Distance = FVector::Dist(GetActorLocation(), TargetPlayer->GetActorLocation());
    return Distance <= GetAttackRange();
}

bool ASurvivorsEnemyCharacter::IsPlayerInSightRange() const
{
    if (!TargetPlayer.IsValid())
    {
        return false;
    }

    const float Distance = FVector::Dist(GetActorLocation(), TargetPlayer->GetActorLocation());
    return Distance <= SightRange;
}

void ASurvivorsEnemyCharacter::HandleDeath()
{
    Super::HandleDeath();

    // Grant experience to player
    GrantExperienceToPlayer();

    // Destroy after a short delay
    FTimerHandle DestroyTimer;
    GetWorld()->GetTimerManager().SetTimer(DestroyTimer, [this]()
    {
        Destroy();
    }, 2.0f, false);
}

void ASurvivorsEnemyCharacter::GrantExperienceToPlayer()
{
    if (!ExperienceEffect)
    {
        return;
    }

    // Find the player who should get experience (nearest player or the one who killed this enemy)
    ASurvivorsPlayerCharacter* Player = FindNearestPlayer();
    if (!Player || !Player->GetAbilitySystemComponent())
    {
        return;
    }

    // Create experience effect spec
    FGameplayEffectContextHandle EffectContext = AbilitySystemComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    FGameplayEffectSpecHandle ExperienceSpecHandle = AbilitySystemComponent->MakeOutgoingSpec(ExperienceEffect, GetCharacterLevel(), EffectContext);
    if (ExperienceSpecHandle.IsValid())
    {
        // Set experience amount
        ExperienceSpecHandle.Data->SetSetByCallerMagnitude(FSurvivorsGameplayTags::Get().Effect_ExperienceGain, ExperienceReward);

        // Apply experience to player
        Player->GetAbilitySystemComponent()->ApplyGameplayEffectSpecToSelf(*ExperienceSpecHandle.Data.Get());
    }
}

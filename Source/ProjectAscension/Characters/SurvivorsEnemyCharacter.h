#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "SurvivorsCharacterBase.h"
#include "SurvivorsEnemyCharacter.generated.h"

class UGameplayAbility;
class UGameplayEffect;
class ASurvivorsPlayerCharacter;
class UPawnSensingComponent;

/**
 * ASurvivorsEnemyCharacter
 * 
 * Enemy character for the Survivors game that uses GAS for AI behavior.
 * Automatically pursues and attacks the player character.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API ASurvivorsEnemyCharacter : public ASurvivorsCharacterBase
{
    GENERATED_BODY()

public:
    ASurvivorsEnemyCharacter();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void PossessedBy(AController* NewController) override;

    // AI Behavior
    UFUNCTION(BlueprintCallable, Category = "Survivors|AI")
    virtual void UpdateAI(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Survivors|AI")
    void MoveTowardsTarget();

    UFUNCTION(BlueprintCallable, Category = "Survivors|AI")
    void AttackTarget();

    UFUNCTION(BlueprintCallable, Category = "Survivors|AI")
    ASurvivorsPlayerCharacter* FindNearestPlayer() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|AI")
    bool IsPlayerInAttackRange() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|AI")
    bool IsPlayerInSightRange() const;

    // Death handling
    virtual void HandleDeath();

    UFUNCTION(BlueprintCallable, Category = "Survivors|Death")
    void GrantExperienceToPlayer();

    // Enemy settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|AI")
    float SightRange = 800.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|AI")
    float AttackRange = 100.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|AI")
    float MovementSpeed = 200.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|AI")
    float AttackCooldown = 2.0f;

    // Experience reward
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Rewards")
    float ExperienceReward = 10.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Rewards")
    TSubclassOf<UGameplayEffect> ExperienceEffect;

    // Enemy type for different behaviors
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Type")
    FGameplayTag EnemyType;

    // AI abilities
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Abilities")
    TSubclassOf<UGameplayAbility> PursuitAbilityClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Abilities")
    TSubclassOf<UGameplayAbility> EnemyAttackAbilityClass;

private:
    // AI state
    UPROPERTY()
    TObjectPtr<ASurvivorsPlayerCharacter> TargetPlayer;

    float LastAttackTime = 0.0f;
    bool bHasTarget = false;

    // AI update interval for performance
    float AIUpdateInterval = 0.1f;
    float AIUpdateTimer = 0.0f;

    // Movement
    FVector LastKnownPlayerLocation = FVector::ZeroVector;
    bool bIsMovingToTarget = false;
};

#include "SurvivorsCharacterBase.h"
#include "../GAS/SurvivorsAbilitySystemComponent.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"

ASurvivorsCharacterBase::ASurvivorsCharacterBase()
{
    PrimaryActorTick.bCanEverTick = false;

    // Create ability system component
    AbilitySystemComponent = CreateDefaultSubobject<USurvivorsAbilitySystemComponent>(TEXT("AbilitySystemComponent"));
    AbilitySystemComponent->SetIsReplicated(true);
    AbilitySystemComponent->SetReplicationMode(EGameplayEffectReplicationMode::Mixed);

    // Create attribute set
    AttributeSet = CreateDefaultSubobject<USurvivorsAttributeSet>(TEXT("AttributeSet"));

    // Set default values
    CharacterLevel = 1;

    // Configure character movement
    GetCharacterMovement()->bOrientRotationToMovement = true;
    GetCharacterMovement()->RotationRate = FRotator(0.0f, 540.0f, 0.0f);
    GetCharacterMovement()->JumpZVelocity = 600.0f;
    GetCharacterMovement()->AirControl = 0.2f;

    // Configure collision
    GetCapsuleComponent()->SetCollisionResponseToChannel(ECollisionChannel::ECC_Camera, ECollisionResponse::ECR_Ignore);
}

void ASurvivorsCharacterBase::BeginPlay()
{
    Super::BeginPlay();

    InitializeAbilitySystem();
}

void ASurvivorsCharacterBase::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    Super::EndPlay(EndPlayReason);
}

UAbilitySystemComponent* ASurvivorsCharacterBase::GetAbilitySystemComponent() const
{
    return AbilitySystemComponent;
}

void ASurvivorsCharacterBase::InitializeAbilitySystem()
{
    if (AbilitySystemComponent)
    {
        AbilitySystemComponent->InitAbilityActorInfo(this, this);
        InitializeAttributes();
        InitializeAbilities();
    }
}

void ASurvivorsCharacterBase::InitializeAttributes()
{
    if (AbilitySystemComponent && AttributeSet)
    {
        // Attributes are initialized in the AttributeSet constructor
        // Additional initialization can be done here if needed
    }
}

void ASurvivorsCharacterBase::InitializeAbilities()
{
    if (AbilitySystemComponent)
    {
        // Grant default abilities
        // This will be implemented in derived classes
    }
}

// Getters
int32 ASurvivorsCharacterBase::GetCharacterLevel() const
{
    if (AttributeSet)
    {
        return FMath::FloorToInt(AttributeSet->GetLevel());
    }
    return CharacterLevel;
}

float ASurvivorsCharacterBase::GetHealth() const
{
    if (AttributeSet)
    {
        return AttributeSet->GetHealth();
    }
    return 0.0f;
}

float ASurvivorsCharacterBase::GetMaxHealth() const
{
    if (AttributeSet)
    {
        return AttributeSet->GetMaxHealth();
    }
    return 0.0f;
}

float ASurvivorsCharacterBase::GetHealthPercent() const
{
    const float MaxHP = GetMaxHealth();
    if (MaxHP > 0.0f)
    {
        return GetHealth() / MaxHP;
    }
    return 0.0f;
}

float ASurvivorsCharacterBase::GetExperience() const
{
    if (AttributeSet)
    {
        return AttributeSet->GetExperience();
    }
    return 0.0f;
}

float ASurvivorsCharacterBase::GetExperiencePercent() const
{
    if (AbilitySystemComponent)
    {
        return AbilitySystemComponent->GetExperiencePercent();
    }
    return 0.0f;
}

float ASurvivorsCharacterBase::GetAttackDamage() const
{
    if (AttributeSet)
    {
        return AttributeSet->GetAttackDamage();
    }
    return 0.0f;
}

bool ASurvivorsCharacterBase::IsAlive() const
{
    return GetHealth() > 0.0f;
}

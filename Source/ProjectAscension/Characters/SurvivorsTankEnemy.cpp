#include "SurvivorsTankEnemy.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Sound/SoundBase.h"
#include "Particles/ParticleSystem.h"

ASurvivorsTankEnemy::ASurvivorsTankEnemy()
{
    // Set tank enemy type
    EnemyType = FSurvivorsGameplayTags::Get().Enemy_Type_Tank;

    // Configure for tank behavior - slow but strong
    MovementSpeed = 150.0f;
    SightRange = 600.0f;
    AttackRange = 120.0f;
    AttackCooldown = 3.0f;
    ExperienceReward = 25.0f;

    // Higher level for more health/damage
    CharacterLevel = 2;

    // Configure movement for heavy unit
    GetCharacterMovement()->MaxWalkSpeed = MovementSpeed;
    GetCharacterMovement()->MaxAcceleration = 1024.0f;
    GetCharacterMovement()->BrakingDecelerationWalking = 1024.0f;
    GetCharacterMovement()->RotationRate = FRotator(0.0f, 360.0f, 0.0f);

    // Store original stats
    OriginalMovementSpeed = MovementSpeed;
}

void ASurvivorsTankEnemy::BeginPlay()
{
    Super::BeginPlay();

    CurrentState = ETankEnemyState::Pursuing;
    
    // Store original attack damage for rage mode
    if (AttributeSet)
    {
        OriginalAttackDamage = GetAttackDamage();
    }
}

void ASurvivorsTankEnemy::UpdateAI(float DeltaTime)
{
    if (!IsAlive())
    {
        return;
    }

    StateTimer += DeltaTime;

    // Check for rage mode
    if (!bHasEnteredRage && ShouldEnterRageMode())
    {
        EnterRageMode();
    }

    // Find target
    ASurvivorsPlayerCharacter* NearestPlayer = FindNearestPlayer();
    if (NearestPlayer && IsPlayerInSightRange())
    {
        TargetPlayer = NearestPlayer;
        
        const float DistanceToPlayer = FVector::Dist(GetActorLocation(), NearestPlayer->GetActorLocation());
        const float CurrentTime = GetWorld()->GetTimeSeconds();

        // State machine
        switch (CurrentState)
        {
            case ETankEnemyState::Pursuing:
            {
                // Check for charge attack
                if (CurrentTime - LastChargeTime >= ChargeCooldown && 
                    DistanceToPlayer <= ChargeRange && 
                    DistanceToPlayer > AttackRange * 1.5f)
                {
                    PerformChargeAttack();
                }
                // Check for area attack
                else if (CurrentTime - LastAreaAttackTime >= AreaAttackCooldown && 
                         DistanceToPlayer <= AreaAttackRange)
                {
                    PerformAreaAttack();
                }
                // Regular attack
                else if (IsPlayerInAttackRange())
                {
                    CurrentState = ETankEnemyState::Attacking;
                    AttackTarget();
                }
                else
                {
                    MoveTowardsTarget();
                }
                break;
            }
            
            case ETankEnemyState::Attacking:
            {
                if (IsPlayerInAttackRange())
                {
                    AttackTarget();
                }
                else
                {
                    CurrentState = ETankEnemyState::Pursuing;
                    StateTimer = 0.0f;
                }
                break;
            }
            
            case ETankEnemyState::Charging:
            {
                // Charge ability handles movement
                // Check if charge is complete (simplified timing)
                if (StateTimer >= 2.0f)
                {
                    CurrentState = ETankEnemyState::Pursuing;
                    StateTimer = 0.0f;
                }
                break;
            }
            
            case ETankEnemyState::AreaAttacking:
            {
                // Area attack has a brief windup
                if (StateTimer >= 1.5f)
                {
                    CurrentState = ETankEnemyState::Pursuing;
                    StateTimer = 0.0f;
                }
                break;
            }
            
            case ETankEnemyState::Stunned:
            {
                // Brief stun after certain attacks
                if (StateTimer >= 1.0f)
                {
                    CurrentState = ETankEnemyState::Pursuing;
                    StateTimer = 0.0f;
                }
                break;
            }
        }
    }
    else
    {
        // No target, return to pursuing
        CurrentState = ETankEnemyState::Pursuing;
        StateTimer = 0.0f;
    }
}

void ASurvivorsTankEnemy::PerformChargeAttack()
{
    if (!TargetPlayer.IsValid())
    {
        return;
    }

    CurrentState = ETankEnemyState::Charging;
    StateTimer = 0.0f;
    LastChargeTime = GetWorld()->GetTimeSeconds();

    // Play effects
    if (ChargeSound)
    {
        UGameplayStatics::PlaySoundAtLocation(GetWorld(), ChargeSound, GetActorLocation());
    }
    
    if (ChargeEffect)
    {
        UGameplayStatics::SpawnEmitterAtLocation(GetWorld(), ChargeEffect, GetActorLocation());
    }

    // Try to activate charge ability
    if (ChargeAbilityClass && AbilitySystemComponent)
    {
        AbilitySystemComponent->GiveAbility(FGameplayAbilitySpec(ChargeAbilityClass, GetCharacterLevel(), INDEX_NONE, this));
        
        for (FGameplayAbilitySpec& Spec : AbilitySystemComponent->GetActivatableAbilities())
        {
            if (Spec.Ability && Spec.Ability->IsA(ChargeAbilityClass))
            {
                AbilitySystemComponent->TryActivateAbility(Spec.Handle);
                break;
            }
        }
    }
    else
    {
        // Fallback: simple charge movement
        FVector ChargeDirection = (TargetPlayer->GetActorLocation() - GetActorLocation()).GetSafeNormal();
        GetCharacterMovement()->AddImpulse(ChargeDirection * 1500.0f, true);
    }
}

void ASurvivorsTankEnemy::PerformAreaAttack()
{
    CurrentState = ETankEnemyState::AreaAttacking;
    StateTimer = 0.0f;
    LastAreaAttackTime = GetWorld()->GetTimeSeconds();

    // Play effects
    if (AreaAttackSound)
    {
        UGameplayStatics::PlaySoundAtLocation(GetWorld(), AreaAttackSound, GetActorLocation());
    }
    
    if (AreaAttackEffect)
    {
        UGameplayStatics::SpawnEmitterAtLocation(GetWorld(), AreaAttackEffect, GetActorLocation());
    }

    // Try to activate area attack ability
    if (AreaAttackAbilityClass && AbilitySystemComponent)
    {
        AbilitySystemComponent->GiveAbility(FGameplayAbilitySpec(AreaAttackAbilityClass, GetCharacterLevel(), INDEX_NONE, this));
        
        for (FGameplayAbilitySpec& Spec : AbilitySystemComponent->GetActivatableAbilities())
        {
            if (Spec.Ability && Spec.Ability->IsA(AreaAttackAbilityClass))
            {
                AbilitySystemComponent->TryActivateAbility(Spec.Handle);
                break;
            }
        }
    }
}

void ASurvivorsTankEnemy::EnterRageMode()
{
    if (bIsInRageMode)
    {
        return;
    }

    bIsInRageMode = true;
    bHasEnteredRage = true;

    // Play rage effects
    if (RageSound)
    {
        UGameplayStatics::PlaySoundAtLocation(GetWorld(), RageSound, GetActorLocation());
    }
    
    if (RageEffect)
    {
        UGameplayStatics::SpawnEmitterAtLocation(GetWorld(), RageEffect, GetActorLocation());
    }

    // Apply rage buffs through GAS
    if (RageModeAbilityClass && AbilitySystemComponent)
    {
        AbilitySystemComponent->GiveAbility(FGameplayAbilitySpec(RageModeAbilityClass, GetCharacterLevel(), INDEX_NONE, this));
        
        for (FGameplayAbilitySpec& Spec : AbilitySystemComponent->GetActivatableAbilities())
        {
            if (Spec.Ability && Spec.Ability->IsA(RageModeAbilityClass))
            {
                AbilitySystemComponent->TryActivateAbility(Spec.Handle);
                break;
            }
        }
    }
    else
    {
        // Fallback: direct stat modification
        GetCharacterMovement()->MaxWalkSpeed = OriginalMovementSpeed * RageSpeedMultiplier;
        
        // TODO: Apply damage multiplier through gameplay effect
    }

    UE_LOG(LogTemp, Warning, TEXT("Tank Enemy entered RAGE MODE!"));
}

bool ASurvivorsTankEnemy::ShouldEnterRageMode() const
{
    if (bHasEnteredRage)
    {
        return false;
    }

    const float HealthPercent = GetHealthPercent();
    return HealthPercent <= RageHealthThreshold;
}

void ASurvivorsTankEnemy::HandleDeath()
{
    // Tank enemies have a dramatic death
    if (AreaAttackEffect)
    {
        UGameplayStatics::SpawnEmitterAtLocation(GetWorld(), AreaAttackEffect, GetActorLocation());
    }

    Super::HandleDeath();
}

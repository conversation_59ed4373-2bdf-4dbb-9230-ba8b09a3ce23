#include "SurvivorsFastEnemy.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "GameFramework/CharacterMovementComponent.h"

ASurvivorsFastEnemy::ASurvivorsFastEnemy()
{
    // Set fast enemy type
    EnemyType = FSurvivorsGameplayTags::Get().Enemy_Type_Fast;

    // Configure for speed
    MovementSpeed = 400.0f;
    SightRange = 1000.0f;
    AttackRange = 80.0f;
    AttackCooldown = 1.5f;
    ExperienceReward = 15.0f;

    // Lower health but higher speed
    CharacterLevel = 1;

    // Configure movement
    GetCharacterMovement()->MaxWalkSpeed = MovementSpeed;
    GetCharacterMovement()->MaxAcceleration = 2048.0f;
    GetCharacterMovement()->BrakingDecelerationWalking = 2048.0f;
    GetCharacterMovement()->RotationRate = FRotator(0.0f, 720.0f, 0.0f);
}

void ASurvivorsFastEnemy::BeginPlay()
{
    Super::BeginPlay();

    CurrentState = EFastEnemyState::Pursuing;
}

void ASurvivorsFastEnemy::UpdateAI(float DeltaTime)
{
    if (!IsAlive())
    {
        return;
    }

    // Update retreat timer
    if (bIsRetreating)
    {
        RetreatTimer += DeltaTime;
        if (RetreatTimer >= RetreatDuration)
        {
            bIsRetreating = false;
            RetreatTimer = 0.0f;
            CurrentState = EFastEnemyState::Pursuing;
        }
    }

    // Check if we should retreat
    if (!bIsRetreating && ShouldRetreat())
    {
        StartRetreat();
        return;
    }

    // State machine
    switch (CurrentState)
    {
        case EFastEnemyState::Pursuing:
        {
            // Find and pursue target
            ASurvivorsPlayerCharacter* NearestPlayer = FindNearestPlayer();
            if (NearestPlayer && IsPlayerInSightRange())
            {
                TargetPlayer = NearestPlayer;
                
                // Check if we can dash attack
                const float CurrentTime = GetWorld()->GetTimeSeconds();
                const float DistanceToPlayer = FVector::Dist(GetActorLocation(), NearestPlayer->GetActorLocation());
                
                if (CurrentTime - LastDashTime >= DashCooldown && 
                    DistanceToPlayer <= DashRange && 
                    DistanceToPlayer > AttackRange)
                {
                    PerformDashAttack();
                }
                else if (IsPlayerInAttackRange())
                {
                    CurrentState = EFastEnemyState::Attacking;
                    AttackTarget();
                }
                else
                {
                    MoveTowardsTarget();
                }
            }
            break;
        }
        
        case EFastEnemyState::Attacking:
        {
            if (IsPlayerInAttackRange())
            {
                AttackTarget();
            }
            else
            {
                CurrentState = EFastEnemyState::Pursuing;
            }
            break;
        }
        
        case EFastEnemyState::Dashing:
        {
            // Dash ability will handle movement
            // Check if dash is complete (simplified)
            if (!HasMatchingGameplayTag(FSurvivorsGameplayTags::Get().Character_State_Moving))
            {
                CurrentState = EFastEnemyState::Pursuing;
            }
            break;
        }
        
        case EFastEnemyState::Retreating:
        {
            // Move away from player
            if (!RetreatDirection.IsZero())
            {
                AddMovementInput(RetreatDirection, 1.0f);
            }
            break;
        }
    }
}

void ASurvivorsFastEnemy::PerformDashAttack()
{
    if (!TargetPlayer.IsValid() || !AbilitySystemComponent)
    {
        return;
    }

    CurrentState = EFastEnemyState::Dashing;
    LastDashTime = GetWorld()->GetTimeSeconds();

    // Try to activate dash ability
    if (DashAbilityClass && AbilitySystemComponent)
    {
        AbilitySystemComponent->GiveAbility(FGameplayAbilitySpec(DashAbilityClass, GetCharacterLevel(), INDEX_NONE, this));
        
        // Find the dash ability and try to activate it
        for (FGameplayAbilitySpec& Spec : AbilitySystemComponent->GetActivatableAbilities())
        {
            if (Spec.Ability && Spec.Ability->IsA(DashAbilityClass))
            {
                AbilitySystemComponent->TryActivateAbility(Spec.Handle);
                break;
            }
        }
    }
    else
    {
        // Fallback: simple dash movement
        FVector DashDirection = (TargetPlayer->GetActorLocation() - GetActorLocation()).GetSafeNormal();
        GetCharacterMovement()->AddImpulse(DashDirection * 1000.0f, true);
    }
}

void ASurvivorsFastEnemy::StartRetreat()
{
    if (!TargetPlayer.IsValid())
    {
        return;
    }

    bIsRetreating = true;
    RetreatTimer = 0.0f;
    CurrentState = EFastEnemyState::Retreating;

    // Calculate retreat direction (away from player)
    FVector ToPlayer = TargetPlayer->GetActorLocation() - GetActorLocation();
    RetreatDirection = -ToPlayer.GetSafeNormal();

    // Add some randomness to retreat direction
    FVector RandomOffset = FVector(
        FMath::RandRange(-0.5f, 0.5f),
        FMath::RandRange(-0.5f, 0.5f),
        0.0f
    );
    RetreatDirection = (RetreatDirection + RandomOffset).GetSafeNormal();
}

bool ASurvivorsFastEnemy::ShouldRetreat() const
{
    if (bIsRetreating)
    {
        return false;
    }

    // Retreat when health is low
    const float HealthPercent = GetHealthPercent();
    return HealthPercent <= RetreatHealthThreshold;
}

#include "SurvivorsPlayerCharacter.h"
#include "../GAS/SurvivorsAbilitySystemComponent.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "../GAS/SurvivorsGameplayTags.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/SphereComponent.h"
#include "Components/InputComponent.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputMappingContext.h"
#include "InputAction.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"

ASurvivorsPlayerCharacter::ASurvivorsPlayerCharacter()
{
    PrimaryActorTick.bCanEverTick = true;

    // Setup camera
    SetupCamera();

    // Create attack range component
    AttackRangeComponent = CreateDefaultSubobject<USphereComponent>(TEXT("AttackRangeComponent"));
    AttackRangeComponent->SetupAttachment(RootComponent);
    AttackRangeComponent->SetSphereRadius(AttackRange);
    AttackRangeComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    AttackRangeComponent->SetCollisionResponseToAllChannels(ECR_Ignore);
    AttackRangeComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

    // Configure character movement for top-down
    GetCharacterMovement()->bOrientRotationToMovement = false;
    GetCharacterMovement()->bUseControllerDesiredRotation = false;
    GetCharacterMovement()->bIgnoreBaseRotation = true;
    GetCharacterMovement()->DefaultLandMovementMode = MOVE_Walking;
    GetCharacterMovement()->MaxWalkSpeed = 300.0f;

    // Don't rotate character to camera direction
    bUseControllerRotationPitch = false;
    bUseControllerRotationYaw = false;
    bUseControllerRotationRoll = false;
}

void ASurvivorsPlayerCharacter::SetupCamera()
{
    // Create spring arm component
    SpringArmComponent = CreateDefaultSubobject<USpringArmComponent>(TEXT("SpringArmComponent"));
    SpringArmComponent->SetupAttachment(RootComponent);
    SpringArmComponent->TargetArmLength = DefaultCameraDistance;
    SpringArmComponent->SetRelativeRotation(FRotator(CameraAngle, 0.0f, 0.0f));
    SpringArmComponent->bUsePawnControlRotation = bUsePawnControlRotation;
    SpringArmComponent->bInheritPitch = false;
    SpringArmComponent->bInheritYaw = false;
    SpringArmComponent->bInheritRoll = false;
    SpringArmComponent->bDoCollisionTest = false;

    // Create camera component
    CameraComponent = CreateDefaultSubobject<UCameraComponent>(TEXT("CameraComponent"));
    CameraComponent->SetupAttachment(SpringArmComponent, USpringArmComponent::SocketName);
    CameraComponent->bUsePawnControlRotation = false;
}

void ASurvivorsPlayerCharacter::BeginPlay()
{
    Super::BeginPlay();

    // Setup enhanced input
    SetupEnhancedInput();

    // Update movement speed from attributes
    UpdateMovementSpeed();

    // Bind attribute change delegates
    BindAttributeDelegates();

    // Start auto-attack
    StartAutoAttack();
}

void ASurvivorsPlayerCharacter::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Update auto-attack
    if (bAutoAttackEnabled)
    {
        UpdateAutoAttack(DeltaTime);
    }
}

void ASurvivorsPlayerCharacter::PossessedBy(AController* NewController)
{
    Super::PossessedBy(NewController);

    // Setup enhanced input for server
    SetupEnhancedInput();
}

void ASurvivorsPlayerCharacter::OnRep_PlayerState()
{
    Super::OnRep_PlayerState();

    // Setup enhanced input for client
    SetupEnhancedInput();
}

void ASurvivorsPlayerCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
    Super::SetupPlayerInputComponent(PlayerInputComponent);

    // Cast to enhanced input component
    if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(PlayerInputComponent))
    {
        // Bind movement
        if (MoveAction)
        {
            EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &ASurvivorsPlayerCharacter::Move);
        }

        // Bind look (for potential mouse look)
        if (LookAction)
        {
            EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &ASurvivorsPlayerCharacter::Look);
        }
    }
}

void ASurvivorsPlayerCharacter::SetupEnhancedInput()
{
    if (APlayerController* PlayerController = Cast<APlayerController>(GetController()))
    {
        if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PlayerController->GetLocalPlayer()))
        {
            if (DefaultMappingContext)
            {
                Subsystem->AddMappingContext(DefaultMappingContext, 0);
            }
        }
    }
}

void ASurvivorsPlayerCharacter::Move(const FInputActionValue& Value)
{
    // Get movement vector from input
    MovementInput = Value.Get<FVector2D>();

    if (Controller != nullptr && !MovementInput.IsZero())
    {
        // Find forward and right vectors
        const FRotator Rotation = Controller->GetControlRotation();
        const FRotator YawRotation(0, Rotation.Yaw, 0);

        const FVector ForwardDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::X);
        const FVector RightDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::Y);

        // Add movement input
        AddMovementInput(ForwardDirection, MovementInput.Y);
        AddMovementInput(RightDirection, MovementInput.X);

        // Try to activate movement ability through GAS
        if (AbilitySystemComponent)
        {
            AbilitySystemComponent->TryActivateAbilityByTag(FSurvivorsGameplayTags::Get().Ability_Movement);
        }
    }
}

void ASurvivorsPlayerCharacter::Look(const FInputActionValue& Value)
{
    // For top-down, we might not need look input, but keeping for flexibility
    FVector2D LookAxisVector = Value.Get<FVector2D>();

    if (Controller != nullptr)
    {
        // Add yaw and pitch input to controller
        AddControllerYawInput(LookAxisVector.X);
        AddControllerPitchInput(LookAxisVector.Y);
    }
}

void ASurvivorsPlayerCharacter::GiveDefaultAbilities()
{
    Super::GiveDefaultAbilities();

    if (!AbilitySystemComponent || GetLocalRole() != ROLE_Authority)
    {
        return;
    }

    // Give movement ability
    if (MovementAbilityClass)
    {
        AbilitySystemComponent->GiveAbility(FGameplayAbilitySpec(MovementAbilityClass, GetCharacterLevel(), INDEX_NONE, this));
    }

    // Give auto-attack ability
    if (AutoAttackAbilityClass)
    {
        AbilitySystemComponent->GiveAbility(FGameplayAbilitySpec(AutoAttackAbilityClass, GetCharacterLevel(), INDEX_NONE, this));
    }
}

// Auto-attack system
void ASurvivorsPlayerCharacter::StartAutoAttack()
{
    bAutoAttackEnabled = true;
    AutoAttackTimer = 0.0f;
}

void ASurvivorsPlayerCharacter::StopAutoAttack()
{
    bAutoAttackEnabled = false;
    CurrentTarget.Reset();
}

void ASurvivorsPlayerCharacter::UpdateAutoAttack(float DeltaTime)
{
    if (!bAutoAttackEnabled)
    {
        return;
    }

    AutoAttackTimer += DeltaTime;

    // Check if we can attack
    const float AttackSpeed = GetAttackSpeed();
    const float AttackInterval = AttackSpeed > 0.0f ? (1.0f / AttackSpeed) : AutoAttackInterval;

    if (AutoAttackTimer >= AttackInterval)
    {
        // Find target
        AActor* Target = CurrentTarget.IsValid() ? CurrentTarget.Get() : FindNearestEnemy();

        if (Target && IsEnemyInRange(Target))
        {
            PerformAttack(Target);
            AutoAttackTimer = 0.0f;
        }
        else
        {
            // Clear invalid target
            CurrentTarget.Reset();
        }
    }
}

AActor* ASurvivorsPlayerCharacter::FindNearestEnemy() const
{
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), EnemyClass ? EnemyClass : APawn::StaticClass(), FoundActors);

    AActor* NearestEnemy = nullptr;
    float NearestDistance = FLT_MAX;
    const FVector PlayerLocation = GetActorLocation();

    for (AActor* Actor : FoundActors)
    {
        if (Actor && Actor != this && Actor->IsA<APawn>())
        {
            // Skip if it's a player character
            if (Actor->IsA<ASurvivorsPlayerCharacter>())
            {
                continue;
            }

            const float Distance = FVector::Dist(PlayerLocation, Actor->GetActorLocation());
            if (Distance < NearestDistance && Distance <= GetAttackRange())
            {
                NearestDistance = Distance;
                NearestEnemy = Actor;
            }
        }
    }

    return NearestEnemy;
}

bool ASurvivorsPlayerCharacter::IsEnemyInRange(AActor* Enemy) const
{
    if (!Enemy)
    {
        return false;
    }

    const float Distance = FVector::Dist(GetActorLocation(), Enemy->GetActorLocation());
    return Distance <= GetAttackRange();
}

void ASurvivorsPlayerCharacter::PerformAttack(AActor* Target)
{
    if (!Target || !AbilitySystemComponent)
    {
        return;
    }

    CurrentTarget = Target;

    // Try to activate auto-attack ability through GAS
    if (AbilitySystemComponent->TryActivateAbilityByTag(FSurvivorsGameplayTags::Get().Ability_AutoAttack))
    {
        // Attack ability will handle the actual damage application
    }
}

// Camera controls
void ASurvivorsPlayerCharacter::SetCameraDistance(float Distance)
{
    if (SpringArmComponent)
    {
        SpringArmComponent->TargetArmLength = Distance;
    }
}

float ASurvivorsPlayerCharacter::GetCameraDistance() const
{
    return SpringArmComponent ? SpringArmComponent->TargetArmLength : DefaultCameraDistance;
}

void ASurvivorsPlayerCharacter::BindAttributeDelegates()
{
    if (!AbilitySystemComponent || !AttributeSet)
    {
        return;
    }

    // Bind to movement speed changes using lambda to avoid FOnAttributeChangeData in header
    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetMovementSpeedAttribute())
        .AddLambda([this](const FOnAttributeChangeData& Data) { OnMovementSpeedChanged(); });

    // Bind to attack range changes
    AbilitySystemComponent->GetGameplayAttributeValueChangeDelegate(AttributeSet->GetAttackRangeAttribute())
        .AddLambda([this](const FOnAttributeChangeData& Data) { OnAttackRangeChanged(); });
}

void ASurvivorsPlayerCharacter::OnMovementSpeedChanged()
{
    UpdateMovementSpeed();
}

void ASurvivorsPlayerCharacter::OnAttackRangeChanged()
{
    UpdateAttackRange();
}

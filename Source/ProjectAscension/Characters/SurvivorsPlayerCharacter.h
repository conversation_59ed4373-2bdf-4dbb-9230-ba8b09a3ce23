#pragma once

#include "CoreMinimal.h"
#include "SurvivorsCharacterBase.h"
#include "InputActionValue.h"
#include "SurvivorsPlayerCharacter.generated.h"

class USpringArmComponent;
class UCameraComponent;
class UInputMappingContext;
class UInputAction;
class USphereComponent;

/**
 * ASurvivorsPlayerCharacter
 * 
 * Player character for the Survivors game with top-down perspective,
 * WASD movement, and auto-attack functionality.
 */
UCLASS(BlueprintType)
class PROJECTASCENSION_API ASurvivorsPlayerCharacter : public ASurvivorsCharacterBase
{
    GENERATED_BODY()

public:
    ASurvivorsPlayerCharacter();

    // APawn interface
    virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;
    virtual void PossessedBy(AController* NewController) override;
    virtual void OnRep_PlayerState() override;

    // Input Actions
    UFUNCTION(BlueprintCallable, Category = "Survivors|Input")
    void Move(const FInputActionValue& Value);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Input")
    void Look(const FInputActionValue& Value);

    // Auto-attack system
    UFUNCTION(BlueprintCallable, Category = "Survivors|Combat")
    void StartAutoAttack();

    UFUNCTION(BlueprintCallable, Category = "Survivors|Combat")
    void StopAutoAttack();

    UFUNCTION(BlueprintCallable, Category = "Survivors|Combat")
    AActor* FindNearestEnemy() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Combat")
    bool IsEnemyInRange(AActor* Enemy) const;

    // Camera controls
    UFUNCTION(BlueprintCallable, Category = "Survivors|Camera")
    void SetCameraDistance(float Distance);

    UFUNCTION(BlueprintCallable, Category = "Survivors|Camera")
    float GetCameraDistance() const;

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // Initialize player-specific abilities
    void GiveDefaultAbilities();

    // Setup enhanced input
    void SetupEnhancedInput();

    // Auto-attack logic
    void UpdateAutoAttack(float DeltaTime);
    void PerformAttack(AActor* Target);

    // Camera Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Survivors|Camera")
    TObjectPtr<USpringArmComponent> SpringArmComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Survivors|Camera")
    TObjectPtr<UCameraComponent> CameraComponent;

    // Attack Range Detection
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Survivors|Combat")
    TObjectPtr<USphereComponent> AttackRangeComponent;

    // Enhanced Input
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Input")
    TObjectPtr<UInputMappingContext> DefaultMappingContext;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Input")
    TObjectPtr<UInputAction> MoveAction;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Input")
    TObjectPtr<UInputAction> LookAction;

    // Player-specific abilities
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Abilities")
    TSubclassOf<UGameplayAbility> MovementAbilityClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Abilities")
    TSubclassOf<UGameplayAbility> AutoAttackAbilityClass;

    // Auto-attack settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Combat")
    float AutoAttackInterval = 1.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Combat")
    float AttackRange = 300.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Combat")
    TSubclassOf<APawn> EnemyClass;

    // Camera settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Camera")
    float DefaultCameraDistance = 800.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Camera")
    float CameraAngle = -60.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Camera")
    bool bUsePawnControlRotation = false;

private:
    // Auto-attack state
    bool bAutoAttackEnabled = true;
    float AutoAttackTimer = 0.0f;
    TWeakObjectPtr<AActor> CurrentTarget;

    // Movement input
    FVector2D MovementInput = FVector2D::ZeroVector;

    // Initialize camera setup
    void SetupCamera();

    // Update movement speed based on attributes
    void UpdateMovementSpeed();

    // Bind attribute change delegates
    void BindAttributeDelegates();

    // Attribute change callbacks (simplified to avoid FOnAttributeChangeData in header)
    void OnMovementSpeedChanged();
    void OnAttackRangeChanged();
};

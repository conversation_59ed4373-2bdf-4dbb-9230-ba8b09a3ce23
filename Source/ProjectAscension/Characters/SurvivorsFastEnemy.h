#pragma once

#include "CoreMinimal.h"
#include "SurvivorsEnemyCharacter.h"
#include "SurvivorsFastEnemy.generated.h"

/**
 * ASurvivorsFastEnemy
 * 
 * Fast enemy type that moves quickly but has low health.
 * Uses hit-and-run tactics with dash abilities.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API ASurvivorsFastEnemy : public ASurvivorsEnemyCharacter
{
    GENERATED_BODY()

public:
    ASurvivorsFastEnemy();

protected:
    virtual void BeginPlay() override;
    virtual void UpdateAI(float DeltaTime) override;

    // Fast enemy specific behavior
    UFUNCTION(BlueprintCallable, Category = "Survivors|FastEnemy")
    void PerformDashAttack();

    UFUNCTION(BlueprintCallable, Category = "Survivors|FastEnemy")
    void StartRetreat();

    UFUNCTION(BlueprintCallable, Category = "Survivors|FastEnemy")
    bool ShouldRetreat() const;

    // Dash ability
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Abilities")
    TSubclassOf<UGameplayAbility> DashAbilityClass;

    // Fast enemy settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|FastEnemy")
    float DashCooldown = 5.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|FastEnemy")
    float DashRange = 400.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|FastEnemy")
    float RetreatHealthThreshold = 0.3f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|FastEnemy")
    float RetreatDistance = 300.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|FastEnemy")
    float RetreatDuration = 2.0f;

private:
    // Dash state
    float LastDashTime = 0.0f;
    bool bIsRetreating = false;
    float RetreatTimer = 0.0f;
    FVector RetreatDirection = FVector::ZeroVector;

    // AI behavior states
    enum class EFastEnemyState
    {
        Pursuing,
        Attacking,
        Dashing,
        Retreating
    };

    EFastEnemyState CurrentState = EFastEnemyState::Pursuing;
};

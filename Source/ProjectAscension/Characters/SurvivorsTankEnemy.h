#pragma once

#include "CoreMinimal.h"
#include "SurvivorsEnemyCharacter.h"
#include "SurvivorsTankEnemy.generated.h"

/**
 * ASurvivorsTankEnemy
 * 
 * Tank enemy type that moves slowly but has high health and damage.
 * Uses charge attacks and area damage abilities.
 */
UCLASS(BlueprintType, Blueprintable)
class PROJECTASCENSION_API ASurvivorsTankEnemy : public ASurvivorsEnemyCharacter
{
    GENERATED_BODY()

public:
    ASurvivorsTankEnemy();

protected:
    virtual void BeginPlay() override;
    virtual void UpdateAI(float DeltaTime) override;
    virtual void HandleDeath() override;

    // Tank enemy specific behavior
    UFUNCTION(BlueprintCallable, Category = "Survivors|TankEnemy")
    void PerformChargeAttack();

    UFUNCTION(BlueprintCallable, Category = "Survivors|TankEnemy")
    void PerformAreaAttack();

    UFUNCTION(BlueprintCallable, Category = "Survivors|TankEnemy")
    void EnterRageMode();

    UFUNCTION(BlueprintCallable, Category = "Survivors|TankEnemy")
    bool ShouldEnterRageMode() const;

    // Tank abilities
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Abilities")
    TSubclassOf<UGameplayAbility> ChargeAbilityClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Abilities")
    TSubclassOf<UGameplayAbility> AreaAttackAbilityClass;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Abilities")
    TSubclassOf<UGameplayAbility> RageModeAbilityClass;

    // Tank enemy settings
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|TankEnemy")
    float ChargeRange = 600.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|TankEnemy")
    float ChargeCooldown = 8.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|TankEnemy")
    float AreaAttackRange = 200.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|TankEnemy")
    float AreaAttackCooldown = 6.0f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|TankEnemy")
    float RageHealthThreshold = 0.5f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|TankEnemy")
    float RageSpeedMultiplier = 1.5f;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|TankEnemy")
    float RageDamageMultiplier = 1.3f;

    // Visual effects
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Effects")
    UParticleSystem* ChargeEffect;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Effects")
    UParticleSystem* AreaAttackEffect;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Effects")
    UParticleSystem* RageEffect;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Effects")
    USoundBase* ChargeSound;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Effects")
    USoundBase* AreaAttackSound;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Survivors|Effects")
    USoundBase* RageSound;

private:
    // Tank state
    float LastChargeTime = 0.0f;
    float LastAreaAttackTime = 0.0f;
    bool bIsInRageMode = false;
    bool bHasEnteredRage = false;

    // AI behavior states
    enum class ETankEnemyState
    {
        Pursuing,
        Attacking,
        Charging,
        AreaAttacking,
        Stunned
    };

    ETankEnemyState CurrentState = ETankEnemyState::Pursuing;
    float StateTimer = 0.0f;

    // Original stats for rage mode
    float OriginalMovementSpeed = 0.0f;
    float OriginalAttackDamage = 0.0f;
};

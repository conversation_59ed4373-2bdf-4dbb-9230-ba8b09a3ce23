#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "AbilitySystemInterface.h"
#include "SurvivorsCharacterBase.generated.h"

class USurvivorsAbilitySystemComponent;
class USurvivorsAttributeSet;

/**
 * ASurvivorsCharacterBase
 * 
 * Base character class for the Survivors game.
 * Implements the Ability System Interface and provides common functionality.
 */
UCLASS(Abstract, BlueprintType, Blueprintable)
class PROJECTASCENSION_API ASurvivorsCharacterBase : public ACharacter, public IAbilitySystemInterface
{
    GENERATED_BODY()

public:
    ASurvivorsCharacterBase();

    // IAbilitySystemInterface
    virtual UAbilitySystemComponent* GetAbilitySystemComponent() const override;

    // Character level and stats
    UFUNCTION(BlueprintCallable, Category = "Survivors|Character")
    int32 GetCharacterLevel() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Character")
    float GetHealth() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Character")
    float GetMaxHealth() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Character")
    float GetHealthPercent() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Character")
    float GetExperience() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Character")
    float GetExperiencePercent() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Character")
    float GetAttackDamage() const;

    UFUNCTION(BlueprintCallable, Category = "Survivors|Character")
    bool IsAlive() const;

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    // Ability System Component
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Survivors|AbilitySystem")
    TObjectPtr<USurvivorsAbilitySystemComponent> AbilitySystemComponent;

    // Attribute Set
    UPROPERTY()
    TObjectPtr<USurvivorsAttributeSet> AttributeSet;

    // Character level
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Survivors|Character")
    int32 CharacterLevel = 1;

    // Initialize ability system
    virtual void InitializeAbilitySystem();

    // Initialize attributes
    virtual void InitializeAttributes();

    // Initialize abilities
    virtual void InitializeAbilities();
};

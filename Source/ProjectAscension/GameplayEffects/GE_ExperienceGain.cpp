#include "GE_ExperienceGain.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "../GAS/SurvivorsGameplayTags.h"

UGE_ExperienceGain::UGE_ExperienceGain()
{
    // Set as instant effect
    DurationPolicy = EGameplayEffectDurationType::Instant;

    // Add experience modifier
    FGameplayModifierInfo ExperienceModifier;
    ExperienceModifier.ModifierMagnitude = FScalableFloat(0.0f); // Will be set by caller
    ExperienceModifier.ModifierOp = EGameplayModOp::Additive;
    ExperienceModifier.Attribute = USurvivorsAttributeSet::GetIncomingExperienceAttribute();

    Modifiers.Add(ExperienceModifier);

    // Add effect tags
    InheritableOwnedTagsContainer.Added.AddTag(FSurvivorsGameplayTags::Get().Effect_ExperienceGain);
}

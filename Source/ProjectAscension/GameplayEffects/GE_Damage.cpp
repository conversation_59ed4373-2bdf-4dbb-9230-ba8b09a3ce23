#include "GE_Damage.h"
#include "../GAS/SurvivorsAttributeSet.h"
#include "../GAS/SurvivorsGameplayTags.h"

UGE_Damage::UGE_Damage()
{
    // Set as instant effect
    DurationPolicy = EGameplayEffectDurationType::Instant;

    // Add damage modifier
    FGameplayModifierInfo DamageModifier;
    DamageModifier.ModifierMagnitude = FScalableFloat(0.0f); // Will be set by caller
    DamageModifier.ModifierOp = EGameplayModOp::Additive;
    DamageModifier.Attribute = USurvivorsAttributeSet::GetIncomingDamageAttribute();

    Modifiers.Add(DamageModifier);

    // Add effect tags
    InheritableOwnedTagsContainer.Added.AddTag(FSurvivorsGameplayTags::Get().Effect_Damage);
    
    // Add damage type tag (can be overridden by specific damage effects)
    InheritableOwnedTagsContainer.Added.AddTag(FSurvivorsGameplayTags::Get().Damage_Physical);
}

// Copyright Epic Games, Inc. All Rights Reserved.

#include "ProjectAscension.h"
#include "Modules/ModuleManager.h"
#include "GAS/SurvivorsGameplayTags.h"

#if WITH_EDITOR
#include "Items/ItemDefinitionNodeSpawner.h"
#include "Items/ItemDefinitionEditorModule.h"
#endif

class FProjectAscensionModule : public FDefaultGameModuleImpl
{
public:
    virtual void StartupModule() override
    {
        FDefaultGameModuleImpl::StartupModule();

        // Initialize native gameplay tags
        FSurvivorsGameplayTags::InitializeNativeTags();
    }
};

IMPLEMENT_PRIMARY_GAME_MODULE( FProjectAscensionModule, ProjectAscension, "ProjectAscension" );

#if WITH_EDITOR
class FProjectAscensionEditorModule : public FDefaultGameModuleImpl
{
public:
    virtual void StartupModule() override
    {
        FDefaultGameModuleImpl::StartupModule();
        
        // Register our custom Blueprint nodes
        FItemDefinitionNodeSpawner::RegisterNodeSpawners();
    }

    virtual void ShutdownModule() override
    {
        // Cleanup custom nodes
        FItemDefinitionNodeSpawner::UnregisterNodeSpawners();
        
        FDefaultGameModuleImpl::ShutdownModule();
    }
};

// Use the editor module when in editor builds
IMPLEMENT_MODULE(FProjectAscensionEditorModule, ProjectAscension)
#endif
